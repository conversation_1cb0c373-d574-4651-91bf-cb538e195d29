<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [STM32G030\STM32G030.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image STM32G030\STM32G030.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060528: Last Updated: Thu Jun 19 12:41:48 2025
<BR><P>
<H3>Maximum Stack Usage =        448 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; initEsp8266 &rArr; ESP8266_Link_Server &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[10]">ADC1_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10]">ADC1_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[10]">ADC1_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[24]">ADC_DMAConvCplt</a> from stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt) referenced from stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[26]">ADC_DMAError</a> from stm32g0xx_hal_adc.o(i.ADC_DMAError) referenced from stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[25]">ADC_DMAHalfConvCplt</a> from stm32g0xx_hal_adc.o(i.ADC_DMAHalfConvCplt) referenced from stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
 <LI><a href="#[f]">DMA1_Ch4_5_DMAMUX1_OVR_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[d]">DMA1_Channel1_IRQHandler</a> from stm32g0xx_it.o(i.DMA1_Channel1_IRQHandler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[e]">DMA1_Channel2_3_IRQHandler</a> from stm32g0xx_it.o(i.DMA1_Channel2_3_IRQHandler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[a]">EXTI0_1_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[b]">EXTI2_3_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[c]">EXTI4_15_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[8]">FLASH_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32g0xx_it.o(i.HardFault_Handler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[17]">I2C1_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[18]">I2C2_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32g0xx_it.o(i.NMI_Handler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from stm32g0xx_it.o(i.PendSV_Handler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[9]">RCC_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[7]">RTC_TAMP_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[19]">SPI1_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[1a]">SPI2_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from stm32g0xx_it.o(i.SVC_Handler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from stm32g0xx_it.o(i.SysTick_Handler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[1e]">SystemInit</a> from system_stm32g0xx.o(i.SystemInit) referenced from startup_stm32g030xx.o(.text)
 <LI><a href="#[14]">TIM14_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[15]">TIM16_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[16]">TIM17_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[11]">TIM1_BRK_UP_TRG_COM_IRQHandler</a> from stm32g0xx_it.o(i.TIM1_BRK_UP_TRG_COM_IRQHandler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[12]">TIM1_CC_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[13]">TIM3_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[27]">UART_DMAAbortOnError</a> from stm32g0xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[2a]">UART_DMAError</a> from stm32g0xx_hal_uart.o(i.UART_DMAError) referenced from stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
 <LI><a href="#[28]">UART_DMAReceiveCplt</a> from stm32g0xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
 <LI><a href="#[29]">UART_DMARxHalfCplt</a> from stm32g0xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
 <LI><a href="#[1b]">USART1_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[1c]">USART2_IRQHandler</a> from stm32g0xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[6]">WWDG_IRQHandler</a> from startup_stm32g030xx.o(.text) referenced from startup_stm32g030xx.o(RESET)
 <LI><a href="#[1f]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32g030xx.o(.text)
 <LI><a href="#[21]">_sbackspace</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[22]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[20]">_sgetc</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[2c]">_sputc</a> from printf8.o(i._sputc) referenced from printf8.o(i.__0sprintf$8)
 <LI><a href="#[2b]">fputc</a> from main.o(i.fputc) referenced from printf8.o(i.__0printf$8)
 <LI><a href="#[23]">isspace</a> from isspace_c.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[1d]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1f]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(.text)
</UL>
<P><STRONG><a name="[e2]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[2d]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[40]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[e3]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[e4]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[e5]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[e6]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[e7]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>DMA1_Ch4_5_DMAMUX1_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>EXTI0_1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>EXTI2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>EXTI4_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>I2C2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>RTC_TAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIM16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIM17_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32g030xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[e8]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[30]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ControlVoltageScaling
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcDMAMUXChannelBaseAndMask
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ultrasonic_val
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_SensorData
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[e9]"></a>__aeabi_idiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, idiv.o(.text), UNUSED)

<P><STRONG><a name="[2f]"></a>__aeabi_idivmod</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, idiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>

<P><STRONG><a name="[74]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[72]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
</UL>

<P><STRONG><a name="[ea]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[32]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[eb]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[ec]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[31]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_rxbuffClear
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATRecv
</UL>

<P><STRONG><a name="[56]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_IO_OUT
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_IO_IN
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Link_Server
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FS_DHT11_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DisplayDeviceData
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
</UL>

<P><STRONG><a name="[ed]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[33]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[51]"></a>strstr</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ExecuteCommand
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATRecv
</UL>

<P><STRONG><a name="[58]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_rxbuffClear
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawFont_GBK16
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
</UL>

<P><STRONG><a name="[52]"></a>strtok</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, strtok.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATRecv
</UL>

<P><STRONG><a name="[34]"></a>__0sscanf</STRONG> (Thumb, 50 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_string
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
</UL>

<P><STRONG><a name="[45]"></a>_scanf_string</STRONG> (Thumb, 232 bytes, Stack size 48 bytes, _scanf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _scanf_string
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[36]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
</UL>

<P><STRONG><a name="[38]"></a>__aeabi_ui2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; __aeabi_llsl
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
</UL>

<P><STRONG><a name="[3a]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2uiz &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
</UL>

<P><STRONG><a name="[ee]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>__aeabi_cdcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
</UL>

<P><STRONG><a name="[3c]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[3b]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>

<P><STRONG><a name="[ef]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[35]"></a>__vfscanf_char</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_string
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[20]"></a>_sgetc</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[21]"></a>_sbackspace</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[f0]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[37]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[39]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _double_epilogue &rArr; __aeabi_llsl
</UL>
<BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
</UL>

<P><STRONG><a name="[2e]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[f1]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[3d]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[f2]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[23]"></a>isspace</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, isspace_c.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ctype_lookup
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[3e]"></a>__vfscanf</STRONG> (Thumb, 812 bytes, Stack size 80 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __vfscanf &rArr; _scanf_string
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real (Weak Reference)
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_longlong (Weak Reference)
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int (Weak Reference)
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_string
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[41]"></a>__ctype_lookup</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, ctype_c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[f3]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[f4]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[4b]"></a>ADC_Enable</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, stm32g0xx_hal_adc.o(i.ADC_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_Enable
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>

<P><STRONG><a name="[5b]"></a>Blink_Leds</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, main.o(i.Blink_Leds))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Blink_Leds &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysis_Packet
</UL>

<P><STRONG><a name="[e1]"></a>Color_Data</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp_p9813.o(i.Color_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Color_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rgb_setValue
</UL>

<P><STRONG><a name="[5d]"></a>DHT11_Check</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, dht11.o(i.DHT11_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DHT11_Check &rArr; DHT11_IO_IN &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_IO_IN
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FS_DHT11_Init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Data
</UL>

<P><STRONG><a name="[63]"></a>DHT11_Read_Bit</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, dht11.o(i.DHT11_Read_Bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DHT11_Read_Bit &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Byte
</UL>

<P><STRONG><a name="[64]"></a>DHT11_Read_Byte</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, dht11.o(i.DHT11_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DHT11_Read_Byte &rArr; DHT11_Read_Bit &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Bit
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Data
</UL>

<P><STRONG><a name="[65]"></a>DHT11_Read_Data</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, dht11.o(i.DHT11_Read_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DHT11_Read_Data &rArr; DHT11_Rst &rArr; DHT11_IO_OUT &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Rst
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Byte
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_SensorData
</UL>

<P><STRONG><a name="[66]"></a>DHT11_Rst</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, dht11.o(i.DHT11_Rst))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DHT11_Rst &rArr; DHT11_IO_OUT &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_IO_OUT
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FS_DHT11_Init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Data
</UL>

<P><STRONG><a name="[d]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g0xx_it.o(i.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA1_Channel1_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>DMA1_Channel2_3_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g0xx_it.o(i.DMA1_Channel2_3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DMA1_Channel2_3_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DisplayDeviceData</STRONG> (Thumb, 824 bytes, Stack size 40 bytes, app_system.o(i.DisplayDeviceData))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = DisplayDeviceData &rArr; Gui_DrawFont_GBK16 &rArr; Gui_DrawPoint &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawFont_GBK16
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_ClearLine
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ExecuteCommand
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
</UL>

<P><STRONG><a name="[6b]"></a>ESP8266_Enable_MultipleId</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, esp8266.o(i.ESP8266_Enable_MultipleId))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = ESP8266_Enable_MultipleId &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[6d]"></a>ESP8266_JoinAP</STRONG> (Thumb, 44 bytes, Stack size 144 bytes, esp8266.o(i.ESP8266_JoinAP))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = ESP8266_JoinAP &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[6e]"></a>ESP8266_Link_Server</STRONG> (Thumb, 122 bytes, Stack size 248 bytes, esp8266.o(i.ESP8266_Link_Server))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = ESP8266_Link_Server &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[6f]"></a>ESP8266_Net_Mode_Choose</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, esp8266.o(i.ESP8266_Net_Mode_Choose))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = ESP8266_Net_Mode_Choose &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[70]"></a>ESP8266_RESTORE</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, esp8266.o(i.ESP8266_RESTORE))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = ESP8266_RESTORE &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[71]"></a>ESP8266_Read_IP</STRONG> (Thumb, 126 bytes, Stack size 216 bytes, esp8266.o(i.ESP8266_Read_IP))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = ESP8266_Read_IP &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_string
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_rxbuffClear
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[75]"></a>ESP8266_UnvarnishMode</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, esp8266.o(i.ESP8266_UnvarnishMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = ESP8266_UnvarnishMode &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[76]"></a>ESP8266_UnvarnishSend</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, esp8266.o(i.ESP8266_UnvarnishSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = ESP8266_UnvarnishSend &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>

<P><STRONG><a name="[77]"></a>ESP_TASK</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, bsp_esp8266.o(i.ESP_TASK))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = ESP_TASK &rArr; SEND_TASK &rArr; ExecuteCommand &rArr; DisplayDeviceData &rArr; Gui_DrawFont_GBK16 &rArr; Gui_DrawPoint &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rec_WaitAT
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATRecv
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8a]"></a>Error_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[7a]"></a>ExecuteCommand</STRONG> (Thumb, 168 bytes, Stack size 8 bytes, app_system.o(i.ExecuteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = ExecuteCommand &rArr; DisplayDeviceData &rArr; Gui_DrawFont_GBK16 &rArr; Gui_DrawPoint &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DisplayDeviceData
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_Beep_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
</UL>

<P><STRONG><a name="[7c]"></a>FS_DHT11_Init</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, dht11.o(i.FS_DHT11_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = FS_DHT11_Init &rArr; DHT11_Rst &rArr; DHT11_IO_OUT &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Rst
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Check
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[53]"></a>Gui_DrawFont_GBK16</STRONG> (Thumb, 176 bytes, Stack size 88 bytes, bsp_lcd.o(i.Gui_DrawFont_GBK16))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = Gui_DrawFont_GBK16 &rArr; Gui_DrawPoint &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DisplayDeviceData
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATRecv
</UL>

<P><STRONG><a name="[7d]"></a>Gui_DrawPoint</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, bsp_lcd.o(i.Gui_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Gui_DrawPoint &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_SetRegion
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteData_16Bit
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawFont_GBK16
</UL>

<P><STRONG><a name="[80]"></a>HAL_ADC_AnalogWDGConfig</STRONG> (Thumb, 1158 bytes, Stack size 32 bytes, stm32g0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_ADC_AnalogWDGConfig &rArr; LL_ADC_SetAnalogWDMonitChannels
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetAnalogWDMonitChannels
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[82]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 832 bytes, Stack size 40 bytes, stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_ADC_ConfigChannel &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetCommonPathInternalCh
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_GetCommonPathInternalCh
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[48]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[4a]"></a>HAL_ADC_ConvHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAHalfConvCplt
</UL>

<P><STRONG><a name="[49]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.HAL_ADC_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAError
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[85]"></a>HAL_ADC_Init</STRONG> (Thumb, 664 bytes, Stack size 32 bytes, stm32g0xx_hal_adc.o(i.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_SetSamplingTimeCommonChannels
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsInternalRegulatorEnabled
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[86]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, adc.o(i.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[8b]"></a>HAL_ADC_Start_DMA</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_ADC_Start_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_StartConversion
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_IsEnabled
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_Disable
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KeyInit
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>HAL_DMA_Abort</STRONG> (Thumb, 146 bytes, Stack size 0 bytes, stm32g0xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[b0]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[ac]"></a>HAL_DMA_GetError</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32g0xx_hal_dma.o(i.HAL_DMA_GetError))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[67]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 282 bytes, Stack size 16 bytes, stm32g0xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_3_IRQHandler
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
</UL>

<P><STRONG><a name="[89]"></a>HAL_DMA_Init</STRONG> (Thumb, 178 bytes, Stack size 8 bytes, stm32g0xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcDMAMUXRequestGenBaseAndMask
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcDMAMUXChannelBaseAndMask
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>

<P><STRONG><a name="[8d]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 210 bytes, Stack size 24 bytes, stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>

<P><STRONG><a name="[5c]"></a>HAL_Delay</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32g0xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testAT
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Blink_Leds
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Rst
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rec_WaitAT
</UL>

<P><STRONG><a name="[61]"></a>HAL_GPIO_Init</STRONG> (Thumb, 448 bytes, Stack size 16 bytes, stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_IO_OUT
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_IO_IN
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FS_DHT11_Init
</UL>

<P><STRONG><a name="[60]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32g0xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Bit
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Check
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ultrasonic_val
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_SensorData
</UL>

<P><STRONG><a name="[54]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FS_DHT11_Init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Blink_Leds
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Rst
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ultrasonic_val
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RGB_Send_Data
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ExecuteCommand
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rec_WaitAT
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATRecv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_Beep_Status
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysis_Packet
</UL>

<P><STRONG><a name="[4e]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32g0xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setTime
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;compareTime
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
</UL>

<P><STRONG><a name="[d1]"></a>HAL_IncTick</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32g0xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[91]"></a>HAL_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32g0xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[92]"></a>HAL_InitTick</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, stm32g0xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[93]"></a>HAL_MspInit</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, stm32g0xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[bb]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32g0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[95]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[97]"></a>HAL_PWREx_ControlVoltageScaling</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_PWREx_ControlVoltageScaling &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[98]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 474 bytes, Stack size 32 bytes, stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[99]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 442 bytes, Stack size 16 bytes, stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[9c]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32g0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>

<P><STRONG><a name="[9b]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 32 bytes, Stack size 4 bytes, stm32g0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[9a]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 216 bytes, Stack size 32 bytes, stm32g0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[9d]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1442 bytes, Stack size 32 bytes, stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[94]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_SYSTICK_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[a4]"></a>HAL_TIMEx_Break2Callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[a3]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[a6]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[9f]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[9e]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 498 bytes, Stack size 8 bytes, stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_Break2Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_BRK_UP_TRG_COM_IRQHandler
</UL>

<P><STRONG><a name="[a0]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[a1]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[a2]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, app_system.o(i.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[a5]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_tim.o(i.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[c9]"></a>HAL_UARTEx_DisableFifoMode</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[b5]"></a>HAL_UARTEx_RxFifoFullCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[a7]"></a>HAL_UARTEx_SetRxFifoThreshold</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = HAL_UARTEx_SetRxFifoThreshold &rArr; UARTEx_SetNbDataToProcess &rArr; __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[a9]"></a>HAL_UARTEx_SetTxFifoThreshold</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = HAL_UARTEx_SetTxFifoThreshold &rArr; UARTEx_SetNbDataToProcess &rArr; __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[b4]"></a>HAL_UARTEx_TxFifoEmptyCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[b2]"></a>HAL_UARTEx_WakeupCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[aa]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_UART_DMAStop
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[b1]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[af]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 562 bytes, Stack size 32 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_UART_IRQHandler &rArr; HAL_DMA_Abort_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_WakeupCallback
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_TxFifoEmptyCallback
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxFifoFullCallback
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[b6]"></a>HAL_UART_Init</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = HAL_UART_Init &rArr; UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[b7]"></a>HAL_UART_MspInit</STRONG> (Thumb, 300 bytes, Stack size 32 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[bc]"></a>HAL_UART_Receive_DMA</STRONG> (Thumb, 228 bytes, Stack size 16 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_UART_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bd]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, stm32g0xx_it.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_UART_RxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[d3]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[59]"></a>HAL_UART_Transmit</STRONG> (Thumb, 264 bytes, Stack size 48 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
</UL>

<P><STRONG><a name="[d4]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32g0xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[bf]"></a>KeyInit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, key.o(i.KeyInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = KeyInit &rArr; MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7f]"></a>LCD_WriteData_16Bit</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, bsp_lcd.o(i.LCD_WriteData_16Bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_WriteData_16Bit
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Clear
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_ClearLine
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawPoint
</UL>

<P><STRONG><a name="[c3]"></a>Lcd_Clear</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, bsp_lcd.o(i.Lcd_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Lcd_Clear &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteIndex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_SetRegion
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteData_16Bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6a]"></a>Lcd_ClearLine</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, bsp_lcd.o(i.Lcd_ClearLine))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Lcd_ClearLine &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteIndex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_SetRegion
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteData_16Bit
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DisplayDeviceData
</UL>

<P><STRONG><a name="[c5]"></a>Lcd_Init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp_lcd.o(i.Lcd_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Lcd_Init &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteIndex
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7e]"></a>Lcd_SetRegion</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, bsp_lcd.o(i.Lcd_SetRegion))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteIndex
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Clear
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_ClearLine
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawPoint
</UL>

<P><STRONG><a name="[c6]"></a>Lcd_WriteData</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, bsp_lcd.o(i.Lcd_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Lcd_WriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_SetRegion
</UL>

<P><STRONG><a name="[c4]"></a>Lcd_WriteIndex</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, bsp_lcd.o(i.Lcd_WriteIndex))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Clear
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_SetRegion
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_ClearLine
</UL>

<P><STRONG><a name="[c1]"></a>MX_ADC1_Init</STRONG> (Thumb, 238 bytes, Stack size 40 bytes, adc.o(i.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_DMA_Init &rArr; DMA_CalcDMAMUXChannelBaseAndMask &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_AnalogWDGConfig
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KeyInit
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c0]"></a>MX_DMA_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KeyInit
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c7]"></a>MX_GPIO_Init</STRONG> (Thumb, 256 bytes, Stack size 32 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c8]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_DisableFifoMode
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ca]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, usart.o(i.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[cb]"></a>RGB_Send_Data</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, bsp_p9813.o(i.RGB_Send_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RGB_Send_Data &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rgb_setValue
</UL>

<P><STRONG><a name="[78]"></a>SEND_TASK</STRONG> (Thumb, 632 bytes, Stack size 24 bytes, bsp_esp8266.o(i.SEND_TASK))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = SEND_TASK &rArr; ExecuteCommand &rArr; DisplayDeviceData &rArr; Gui_DrawFont_GBK16 &rArr; Gui_DrawPoint &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Blink_Leds
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_SensorData
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ExecuteCommand
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DisplayDeviceData
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;packet_data
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysis_Packet
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_TASK
</UL>

<P><STRONG><a name="[c2]"></a>SPI_WriteData</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, bsp_lcd.o(i.SPI_WriteData))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteIndex
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_WriteData
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteData_16Bit
</UL>

<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32g0xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32g0xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[d2]"></a>SystemClock_Config</STRONG> (Thumb, 142 bytes, Stack size 96 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ControlVoltageScaling
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e]"></a>SystemInit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, system_stm32g0xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(.text)
</UL>
<P><STRONG><a name="[11]"></a>TIM1_BRK_UP_TRG_COM_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32g0xx_it.o(i.TIM1_BRK_UP_TRG_COM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM1_BRK_UP_TRG_COM_IRQHandler &rArr; HAL_TIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[b9]"></a>UART_AdvFeatureConfig</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[ba]"></a>UART_CheckIdleState</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32g0xx_hal_uart.o(i.UART_CheckIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_CheckIdleState &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[b8]"></a>UART_SetConfig</STRONG> (Thumb, 1612 bytes, Stack size 40 bytes, stm32g0xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[be]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
</UL>

<P><STRONG><a name="[1c]"></a>USART2_IRQHandler</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, stm32g0xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USART2_IRQHandler &rArr; HAL_UART_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32g030xx.o(RESET)
</UL>
<P><STRONG><a name="[d5]"></a>__0printf$8</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[f5]"></a>__1printf$8</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8), UNUSED)

<P><STRONG><a name="[50]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testAT
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_rxbuffClear
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rec_WaitAT
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATRecv
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysis_Packet
</UL>

<P><STRONG><a name="[d7]"></a>__0sprintf$8</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printf8.o(i.__0sprintf$8), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[f6]"></a>__1sprintf$8</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0sprintf$8), UNUSED)

<P><STRONG><a name="[57]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0sprintf$8))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Link_Server
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_JoinAP
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Enable_MultipleId
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DisplayDeviceData
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
</UL>

<P><STRONG><a name="[3f]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz))
<BR><BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[cc]"></a>__ARM_common_switch8</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32g0xx_hal_dma.o(i.__ARM_common_switch8))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_common_switch8
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rec_WaitAT
</UL>

<P><STRONG><a name="[f7]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[f8]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[f9]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[d0]"></a>analysis_Packet</STRONG> (Thumb, 322 bytes, Stack size 16 bytes, fs_protocol.o(i.analysis_Packet))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = analysis_Packet &rArr; rgb_setValue &rArr; RGB_Send_Data &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Blink_Leds
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_Beep_Status
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rgb_setValue
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc8
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
</UL>

<P><STRONG><a name="[6c]"></a>cmdAT</STRONG> (Thumb, 314 bytes, Stack size 96 bytes, esp8266.o(i.cmdAT))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_rxbuffClear
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testAT
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_UnvarnishSend
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_UnvarnishMode
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_RESTORE
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Net_Mode_Choose
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Link_Server
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_JoinAP
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Enable_MultipleId
</UL>

<P><STRONG><a name="[cd]"></a>compareTime</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, bsp_softwaretimer.o(i.compareTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = compareTime
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rec_WaitAT
</UL>

<P><STRONG><a name="[da]"></a>crc8</STRONG> (Thumb, 62 bytes, Stack size 12 bytes, fs_crc.o(i.crc8))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = crc8
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;packet_data
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysis_Packet
</UL>

<P><STRONG><a name="[e0]"></a>delay_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dht11.o(i.delay_init))
<BR><BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5f]"></a>delay_us</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dht11.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Rst
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Bit
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Check
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ultrasonic_val
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RGB_Send_Data
</UL>

<P><STRONG><a name="[2b]"></a>fputc</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, main.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = fputc &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf8.o(i.__0printf$8)
</UL>
<P><STRONG><a name="[ce]"></a>get_SensorData</STRONG> (Thumb, 270 bytes, Stack size 32 bytes, app_system.o(i.get_SensorData))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = get_SensorData &rArr; DHT11_Read_Data &rArr; DHT11_Rst &rArr; DHT11_IO_OUT &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Read_Data
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ultrasonic_val
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
</UL>

<P><STRONG><a name="[dd]"></a>get_ultrasonic_val</STRONG> (Thumb, 164 bytes, Stack size 8 bytes, bsp_ultrasonic.o(i.get_ultrasonic_val))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = get_ultrasonic_val &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_SensorData
</UL>

<P><STRONG><a name="[de]"></a>initEsp8266</STRONG> (Thumb, 260 bytes, Stack size 8 bytes, esp8266.o(i.initEsp8266))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = initEsp8266 &rArr; ESP8266_Link_Server &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testAT
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_UnvarnishSend
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_UnvarnishMode
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_RESTORE
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Net_Mode_Choose
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Link_Server
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_JoinAP
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Enable_MultipleId
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1d]"></a>main</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = main &rArr; initEsp8266 &rArr; ESP8266_Link_Server &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_Clear
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KeyInit
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_DMA
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawFont_GBK16
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FS_DHT11_Init
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_TASK
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[cf]"></a>packet_data</STRONG> (Thumb, 106 bytes, Stack size 40 bytes, fs_protocol.o(i.packet_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = packet_data &rArr; crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc8
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEND_TASK
</UL>

<P><STRONG><a name="[73]"></a>reset_rxbuffClear</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, esp8266.o(i.reset_rxbuffClear))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = reset_rxbuffClear &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP8266_Read_IP
</UL>

<P><STRONG><a name="[db]"></a>rgb_setValue</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, bsp_p9813.o(i.rgb_setValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rgb_setValue &rArr; RGB_Send_Data &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RGB_Send_Data
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Color_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysis_Packet
</UL>

<P><STRONG><a name="[5a]"></a>setTime</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, bsp_softwaretimer.o(i.setTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = setTime
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rec_WaitAT
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ATSend
</UL>

<P><STRONG><a name="[7b]"></a>set_Beep_Status</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, bsp_beep.o(i.set_Beep_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = set_Beep_Status
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ExecuteCommand
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysis_Packet
</UL>

<P><STRONG><a name="[df]"></a>testAT</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, esp8266.o(i.testAT))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = testAT &rArr; cmdAT &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdAT
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initEsp8266
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[5e]"></a>DHT11_IO_IN</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, dht11.o(i.DHT11_IO_IN))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DHT11_IO_IN &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Check
</UL>

<P><STRONG><a name="[62]"></a>DHT11_IO_OUT</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, dht11.o(i.DHT11_IO_OUT))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DHT11_IO_OUT &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHT11_Rst
</UL>

<P><STRONG><a name="[24]"></a>ADC_DMAConvCplt</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_DMAConvCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsTriggerSourceSWStart
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LL_ADC_REG_IsConversionOngoing
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[26]"></a>ADC_DMAError</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, stm32g0xx_hal_adc.o(i.ADC_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[25]"></a>ADC_DMAHalfConvCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32g0xx_hal_adc.o(i.ADC_DMAHalfConvCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_DMAHalfConvCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
</UL>
<P><STRONG><a name="[8c]"></a>LL_ADC_Disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>

<P><STRONG><a name="[4d]"></a>LL_ADC_Enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>

<P><STRONG><a name="[83]"></a>LL_ADC_GetCommonPathInternalCh</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_GetCommonPathInternalCh))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[4c]"></a>LL_ADC_IsEnabled</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_IsEnabled))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>

<P><STRONG><a name="[87]"></a>LL_ADC_IsInternalRegulatorEnabled</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[47]"></a>LL_ADC_REG_IsConversionOngoing</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_AnalogWDGConfig
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[46]"></a>LL_ADC_REG_IsTriggerSourceSWStart</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart))
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMAConvCplt
</UL>

<P><STRONG><a name="[8e]"></a>LL_ADC_REG_StartConversion</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_REG_StartConversion))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start_DMA
</UL>

<P><STRONG><a name="[81]"></a>LL_ADC_SetAnalogWDMonitChannels</STRONG> (Thumb, 50 bytes, Stack size 12 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LL_ADC_SetAnalogWDMonitChannels
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_AnalogWDGConfig
</UL>

<P><STRONG><a name="[84]"></a>LL_ADC_SetCommonPathInternalCh</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>

<P><STRONG><a name="[88]"></a>LL_ADC_SetSamplingTimeCommonChannels</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, stm32g0xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonChannels))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LL_ADC_SetSamplingTimeCommonChannels
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[68]"></a>DMA_CalcDMAMUXChannelBaseAndMask</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA_CalcDMAMUXChannelBaseAndMask &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[8f]"></a>DMA_CalcDMAMUXRequestGenBaseAndMask</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[90]"></a>DMA_SetConfig</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32g0xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[96]"></a>__NVIC_SetPriority</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, stm32g0xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[27]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, stm32g0xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[2a]"></a>UART_DMAError</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, stm32g0xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
</UL>
<P><STRONG><a name="[28]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32g0xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UART_RxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
</UL>
<P><STRONG><a name="[29]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32g0xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMARxHalfCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
</UL>
<P><STRONG><a name="[ae]"></a>UART_EndRxTransfer</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32g0xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[b3]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32g0xx_hal_uart.o(i.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_EndTransmit_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[ad]"></a>UART_EndTxTransfer</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32g0xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[a8]"></a>UARTEx_SetNbDataToProcess</STRONG> (Thumb, 114 bytes, Stack size 40 bytes, stm32g0xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = UARTEx_SetNbDataToProcess &rArr; __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
</UL>

<P><STRONG><a name="[4f]"></a>ATRecv</STRONG> (Thumb, 304 bytes, Stack size 16 bytes, bsp_esp8266.o(i.ATRecv))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = ATRecv &rArr; Gui_DrawFont_GBK16 &rArr; Gui_DrawPoint &rArr; Lcd_SetRegion &rArr; Lcd_WriteIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gui_DrawFont_GBK16
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_TASK
</UL>

<P><STRONG><a name="[55]"></a>ATSend</STRONG> (Thumb, 220 bytes, Stack size 112 bytes, bsp_esp8266.o(i.ATSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = ATSend &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setTime
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_TASK
</UL>

<P><STRONG><a name="[79]"></a>Rec_WaitAT</STRONG> (Thumb, 528 bytes, Stack size 8 bytes, bsp_esp8266.o(i.Rec_WaitAT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Rec_WaitAT &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_switch8
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setTime
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;compareTime
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_TASK
</UL>

<P><STRONG><a name="[d6]"></a>_printf_core</STRONG> (Thumb, 1020 bytes, Stack size 104 bytes, printf8.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$8
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf$8
</UL>

<P><STRONG><a name="[d9]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printf8.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[d8]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printf8.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2c]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printf8.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$8
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf8.o(i.__0sprintf$8)
</UL>
<P><STRONG><a name="[22]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[44]"></a>_scanf_int</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[43]"></a>_scanf_longlong</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[42]"></a>_scanf_real</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<HR></body></html>
