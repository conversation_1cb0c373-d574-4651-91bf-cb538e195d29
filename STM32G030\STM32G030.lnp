--cpu Cortex-M0+
"stm32g030\startup_stm32g030xx.o"
"stm32g030\main.o"
"stm32g030\gpio.o"
"stm32g030\adc.o"
"stm32g030\dma.o"
"stm32g030\tim.o"
"stm32g030\usart.o"
"stm32g030\stm32g0xx_it.o"
"stm32g030\stm32g0xx_hal_msp.o"
"stm32g030\key.o"
"stm32g030\esp8266.o"
"stm32g030\dht11.o"
"stm32g030\stm32g0xx_hal_gpio.o"
"stm32g030\stm32g0xx_hal_adc.o"
"stm32g030\stm32g0xx_hal_adc_ex.o"
"stm32g030\stm32g0xx_ll_adc.o"
"stm32g030\stm32g0xx_hal_rcc.o"
"stm32g030\stm32g0xx_hal_rcc_ex.o"
"stm32g030\stm32g0xx_ll_rcc.o"
"stm32g030\stm32g0xx_hal_flash.o"
"stm32g030\stm32g0xx_hal_flash_ex.o"
"stm32g030\stm32g0xx_hal_dma.o"
"stm32g030\stm32g0xx_hal_dma_ex.o"
"stm32g030\stm32g0xx_hal_pwr.o"
"stm32g030\stm32g0xx_hal_pwr_ex.o"
"stm32g030\stm32g0xx_hal_cortex.o"
"stm32g030\stm32g0xx_hal.o"
"stm32g030\stm32g0xx_hal_exti.o"
"stm32g030\stm32g0xx_hal_tim.o"
"stm32g030\stm32g0xx_hal_tim_ex.o"
"stm32g030\stm32g0xx_hal_uart.o"
"stm32g030\stm32g0xx_hal_uart_ex.o"
"stm32g030\system_stm32g0xx.o"
"stm32g030\fs_crc.o"
"stm32g030\fs_protocol.o"
"stm32g030\bsp_esp8266.o"
"stm32g030\bsp_lcd.o"
"stm32g030\bsp_p9813.o"
"stm32g030\bsp_softwaretimer.o"
"stm32g030\bsp_ultrasonic.o"
"stm32g030\app_system.o"
"stm32g030\bsp_beep.o"
--library_type=microlib --strict --scatter "STM32G030\STM32G030.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32G030.map" -o STM32G030\STM32G030.axf