


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       ***********************
    2 00000000         ;* File Name          : startup_stm32g030xx.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Description        : STM32G030xx devices vector table
                        for MDK-ARM toolchain.
    5 00000000         ;*                      This module performs:
    6 00000000         ;*                      - Set the initial SP
    7 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
    8 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
    9 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   10 00000000         ;*                        calls main()).
   11 00000000         ;*                      After Reset the CortexM0 process
                       or is in Thread mode,
   12 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   13 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>
   14 00000000         ;*******************************************************
                       *********************** 
   15 00000000         ;* @attention
   16 00000000         ;*
   17 00000000         ;* Copyright (c) 2019 STMicroelectronics. All rights res
                       erved.
   18 00000000         ;*
   19 00000000         ;* This software component is licensed by ST under BSD 3
                       -Clause license,
   20 00000000         ;* the "License"; You may not use this file except in co
                       mpliance with the 
   21 00000000         ;* License. You may obtain a copy of the License at:
   22 00000000         ;*                        opensource.org/licenses/BSD-3-
                       Clause
   23 00000000         ;*
   24 00000000         ;*******************************************************
                       ***********************
   25 00000000         
   26 00000000         ; Amount of memory (in bytes) allocated for Stack
   27 00000000         ; Tailor this value to your application needs
   28 00000000         ; <h> Stack Configuration
   29 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   30 00000000         ; </h>
   31 00000000         
   32 00000000 00000800 
                       Stack_Size
                               EQU              0x800
   33 00000000         
   34 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   35 00000000         Stack_Mem
                               SPACE            Stack_Size
   36 00000800         __initial_sp
   37 00000800         
   38 00000800         
   39 00000800         ; <h> Heap Configuration
   40 00000800         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   41 00000800         ; </h>
   42 00000800         



ARM Macro Assembler    Page 2 


   43 00000800 00000100 
                       Heap_Size
                               EQU              0x100
   44 00000800         
   45 00000800                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   46 00000000         __heap_base
   47 00000000         Heap_Mem
                               SPACE            Heap_Size
   48 00000100         __heap_limit
   49 00000100         
   50 00000100                 PRESERVE8
   51 00000100                 THUMB
   52 00000100         
   53 00000100         
   54 00000100         ; Vector Table Mapped to Address 0 at Reset
   55 00000100                 AREA             RESET, DATA, READONLY
   56 00000000                 EXPORT           __Vectors
   57 00000000                 EXPORT           __Vectors_End
   58 00000000                 EXPORT           __Vectors_Size
   59 00000000         
   60 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   61 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   62 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   63 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   64 00000010 00000000        DCD              0           ; Reserved
   65 00000014 00000000        DCD              0           ; Reserved
   66 00000018 00000000        DCD              0           ; Reserved
   67 0000001C 00000000        DCD              0           ; Reserved
   68 00000020 00000000        DCD              0           ; Reserved
   69 00000024 00000000        DCD              0           ; Reserved
   70 00000028 00000000        DCD              0           ; Reserved
   71 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   72 00000030 00000000        DCD              0           ; Reserved
   73 00000034 00000000        DCD              0           ; Reserved
   74 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   75 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   76 00000040         
   77 00000040         ; External Interrupts
   78 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog
   79 00000044 00000000        DCD              0           ; Reserved
   80 00000048 00000000        DCD              RTC_TAMP_IRQHandler ; RTC throu
                                                            gh EXTI Line
   81 0000004C 00000000        DCD              FLASH_IRQHandler ; FLASH
   82 00000050 00000000        DCD              RCC_IRQHandler ; RCC
   83 00000054 00000000        DCD              EXTI0_1_IRQHandler 
                                                            ; EXTI Line 0 and 1
                                                            
   84 00000058 00000000        DCD              EXTI2_3_IRQHandler 
                                                            ; EXTI Line 2 and 3
                                                            
   85 0000005C 00000000        DCD              EXTI4_15_IRQHandler 
                                                            ; EXTI Line 4 to 15



ARM Macro Assembler    Page 3 


                                                            
   86 00000060 00000000        DCD              0           ; Reserved
   87 00000064 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   88 00000068 00000000        DCD              DMA1_Channel2_3_IRQHandler ; DM
                                                            A1 Channel 2 and Ch
                                                            annel 3
   89 0000006C 00000000        DCD              DMA1_Ch4_5_DMAMUX1_OVR_IRQHandl
er 
                                                            ; DMA1 Channel 4 to
                                                             Channel 5, DMAMUX1
                                                             overrun
   90 00000070 00000000        DCD              ADC1_IRQHandler ; ADC1
   91 00000074 00000000        DCD              TIM1_BRK_UP_TRG_COM_IRQHandler 
                                                            ; TIM1 Break, Updat
                                                            e, Trigger and Comm
                                                            utation
   92 00000078 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
   93 0000007C 00000000        DCD              0           ; Reserved
   94 00000080 00000000        DCD              TIM3_IRQHandler ; TIM3
   95 00000084 00000000        DCD              0           ; Reserved
   96 00000088 00000000        DCD              0           ; Reserved
   97 0000008C 00000000        DCD              TIM14_IRQHandler ; TIM14
   98 00000090 00000000        DCD              0           ; Reserved
   99 00000094 00000000        DCD              TIM16_IRQHandler ; TIM16
  100 00000098 00000000        DCD              TIM17_IRQHandler ; TIM17
  101 0000009C 00000000        DCD              I2C1_IRQHandler ; I2C1
  102 000000A0 00000000        DCD              I2C2_IRQHandler ; I2C2
  103 000000A4 00000000        DCD              SPI1_IRQHandler ; SPI1
  104 000000A8 00000000        DCD              SPI2_IRQHandler ; SPI2
  105 000000AC 00000000        DCD              USART1_IRQHandler ; USART1
  106 000000B0 00000000        DCD              USART2_IRQHandler ; USART2
  107 000000B4 00000000        DCD              0           ; Reserved
  108 000000B8 00000000        DCD              0           ; Reserved
  109 000000BC 00000000        DCD              0           ; Reserved
  110 000000C0         
  111 000000C0         __Vectors_End
  112 000000C0         
  113 000000C0 000000C0 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  114 000000C0         
  115 000000C0                 AREA             |.text|, CODE, READONLY
  116 00000000         
  117 00000000         ; Reset handler routine
  118 00000000         Reset_Handler
                               PROC
  119 00000000                 EXPORT           Reset_Handler                 [
WEAK]
  120 00000000                 IMPORT           __main
  121 00000000                 IMPORT           SystemInit
  122 00000000 4804            LDR              R0, =SystemInit
  123 00000002 4780            BLX              R0
  124 00000004 4804            LDR              R0, =__main
  125 00000006 4700            BX               R0
  126 00000008                 ENDP
  127 00000008         
  128 00000008         ; Dummy Exception Handlers (infinite loops which can be 



ARM Macro Assembler    Page 4 


                       modified)
  129 00000008         
  130 00000008         NMI_Handler
                               PROC
  131 00000008                 EXPORT           NMI_Handler                    
[WEAK]
  132 00000008 E7FE            B                .
  133 0000000A                 ENDP
  135 0000000A         HardFault_Handler
                               PROC
  136 0000000A                 EXPORT           HardFault_Handler              
[WEAK]
  137 0000000A E7FE            B                .
  138 0000000C                 ENDP
  139 0000000C         SVC_Handler
                               PROC
  140 0000000C                 EXPORT           SVC_Handler                    
[WEAK]
  141 0000000C E7FE            B                .
  142 0000000E                 ENDP
  143 0000000E         PendSV_Handler
                               PROC
  144 0000000E                 EXPORT           PendSV_Handler                 
[WEAK]
  145 0000000E E7FE            B                .
  146 00000010                 ENDP
  147 00000010         SysTick_Handler
                               PROC
  148 00000010                 EXPORT           SysTick_Handler                
[WEAK]
  149 00000010 E7FE            B                .
  150 00000012                 ENDP
  151 00000012         
  152 00000012         Default_Handler
                               PROC
  153 00000012         
  154 00000012                 EXPORT           WWDG_IRQHandler                
[WEAK]
  155 00000012                 EXPORT           RTC_TAMP_IRQHandler            
[WEAK]
  156 00000012                 EXPORT           FLASH_IRQHandler               
[WEAK]
  157 00000012                 EXPORT           RCC_IRQHandler                 
[WEAK]
  158 00000012                 EXPORT           EXTI0_1_IRQHandler             
[WEAK]
  159 00000012                 EXPORT           EXTI2_3_IRQHandler             
[WEAK]
  160 00000012                 EXPORT           EXTI4_15_IRQHandler            
[WEAK]
  161 00000012                 EXPORT           DMA1_Channel1_IRQHandler       
[WEAK]
  162 00000012                 EXPORT           DMA1_Channel2_3_IRQHandler     
[WEAK]
  163 00000012                 EXPORT           DMA1_Ch4_5_DMAMUX1_OVR_IRQHandl
er [WEAK]
  164 00000012                 EXPORT           ADC1_IRQHandler                
[WEAK]
  165 00000012                 EXPORT           TIM1_BRK_UP_TRG_COM_IRQHandler 



ARM Macro Assembler    Page 5 


[WEAK]
  166 00000012                 EXPORT           TIM1_CC_IRQHandler             
[WEAK]
  167 00000012                 EXPORT           TIM3_IRQHandler                
[WEAK]
  168 00000012                 EXPORT           TIM14_IRQHandler               
[WEAK]
  169 00000012                 EXPORT           TIM16_IRQHandler               
[WEAK]
  170 00000012                 EXPORT           TIM17_IRQHandler               
[WEAK]
  171 00000012                 EXPORT           I2C1_IRQHandler                
[WEAK]
  172 00000012                 EXPORT           I2C2_IRQHandler                
[WEAK]
  173 00000012                 EXPORT           SPI1_IRQHandler                
[WEAK]
  174 00000012                 EXPORT           SPI2_IRQHandler                
[WEAK]
  175 00000012                 EXPORT           USART1_IRQHandler              
[WEAK]
  176 00000012                 EXPORT           USART2_IRQHandler              
[WEAK]
  177 00000012         
  178 00000012         
  179 00000012         WWDG_IRQHandler
  180 00000012         RTC_TAMP_IRQHandler
  181 00000012         FLASH_IRQHandler
  182 00000012         RCC_IRQHandler
  183 00000012         EXTI0_1_IRQHandler
  184 00000012         EXTI2_3_IRQHandler
  185 00000012         EXTI4_15_IRQHandler
  186 00000012         DMA1_Channel1_IRQHandler
  187 00000012         DMA1_Channel2_3_IRQHandler
  188 00000012         DMA1_Ch4_5_DMAMUX1_OVR_IRQHandler
  189 00000012         ADC1_IRQHandler
  190 00000012         TIM1_BRK_UP_TRG_COM_IRQHandler
  191 00000012         TIM1_CC_IRQHandler
  192 00000012         TIM3_IRQHandler
  193 00000012         TIM14_IRQHandler
  194 00000012         TIM16_IRQHandler
  195 00000012         TIM17_IRQHandler
  196 00000012         I2C1_IRQHandler
  197 00000012         I2C2_IRQHandler
  198 00000012         SPI1_IRQHandler
  199 00000012         SPI2_IRQHandler
  200 00000012         USART1_IRQHandler
  201 00000012         USART2_IRQHandler
  202 00000012         
  203 00000012 E7FE            B                .
  204 00000014         
  205 00000014                 ENDP
  206 00000014         
  207 00000014                 ALIGN
  208 00000014         
  209 00000014         ;*******************************************************
                       ************************
  210 00000014         ; User Stack and Heap initialization
  211 00000014         ;*******************************************************



ARM Macro Assembler    Page 6 


                       ************************
  212 00000014                 IF               :DEF:__MICROLIB
  213 00000014         
  214 00000014                 EXPORT           __initial_sp
  215 00000014                 EXPORT           __heap_base
  216 00000014                 EXPORT           __heap_limit
  217 00000014         
  218 00000014                 ELSE
  233                          ENDIF
  234 00000014         
  235 00000014                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --apcs=inter
work --depend=stm32g030\startup_stm32g030xx.d -ostm32g030\startup_stm32g030xx.o
 -I.\RTE\_STM32G030 -ID:\keil5\Keilv5\ARM\PACK\ARM\CMSIS\5.4.0\CMSIS\Core\Inclu
de -ID:\keil5\Keilv5\ARM\PACK\Keil\STM32G0xx_DFP\1.4.0\Drivers\CMSIS\Device\ST\
STM32G0xx\Include --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSIO
N SETA 541" --predefine="STM32G030xx SETA 1" --predefine="_RTE_ SETA 1" --list=
startup_stm32g030xx.lst startup_stm32g030xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 34 in file startup_stm32g030xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 35 in file startup_stm32g030xx.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000800

Symbol: __initial_sp
   Definitions
      At line 36 in file startup_stm32g030xx.s
   Uses
      At line 60 in file startup_stm32g030xx.s
      At line 214 in file startup_stm32g030xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 45 in file startup_stm32g030xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 47 in file startup_stm32g030xx.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 46 in file startup_stm32g030xx.s
   Uses
      At line 215 in file startup_stm32g030xx.s
Comment: __heap_base used once
__heap_limit 00000100

Symbol: __heap_limit
   Definitions
      At line 48 in file startup_stm32g030xx.s
   Uses
      At line 216 in file startup_stm32g030xx.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 55 in file startup_stm32g030xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 60 in file startup_stm32g030xx.s
   Uses
      At line 56 in file startup_stm32g030xx.s
      At line 113 in file startup_stm32g030xx.s

__Vectors_End 000000C0

Symbol: __Vectors_End
   Definitions
      At line 111 in file startup_stm32g030xx.s
   Uses
      At line 57 in file startup_stm32g030xx.s
      At line 113 in file startup_stm32g030xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 115 in file startup_stm32g030xx.s
   Uses
      None
Comment: .text unused
ADC1_IRQHandler 00000012

Symbol: ADC1_IRQHandler
   Definitions
      At line 189 in file startup_stm32g030xx.s
   Uses
      At line 90 in file startup_stm32g030xx.s
      At line 164 in file startup_stm32g030xx.s

DMA1_Ch4_5_DMAMUX1_OVR_IRQHandler 00000012

Symbol: DMA1_Ch4_5_DMAMUX1_OVR_IRQHandler
   Definitions
      At line 188 in file startup_stm32g030xx.s
   Uses
      At line 89 in file startup_stm32g030xx.s
      At line 163 in file startup_stm32g030xx.s

DMA1_Channel1_IRQHandler 00000012

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 186 in file startup_stm32g030xx.s
   Uses
      At line 87 in file startup_stm32g030xx.s
      At line 161 in file startup_stm32g030xx.s

DMA1_Channel2_3_IRQHandler 00000012

Symbol: DMA1_Channel2_3_IRQHandler
   Definitions
      At line 187 in file startup_stm32g030xx.s
   Uses
      At line 88 in file startup_stm32g030xx.s
      At line 162 in file startup_stm32g030xx.s

Default_Handler 00000012

Symbol: Default_Handler
   Definitions
      At line 152 in file startup_stm32g030xx.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_1_IRQHandler 00000012

Symbol: EXTI0_1_IRQHandler
   Definitions
      At line 183 in file startup_stm32g030xx.s
   Uses
      At line 83 in file startup_stm32g030xx.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 158 in file startup_stm32g030xx.s

EXTI2_3_IRQHandler 00000012

Symbol: EXTI2_3_IRQHandler
   Definitions
      At line 184 in file startup_stm32g030xx.s
   Uses
      At line 84 in file startup_stm32g030xx.s
      At line 159 in file startup_stm32g030xx.s

EXTI4_15_IRQHandler 00000012

Symbol: EXTI4_15_IRQHandler
   Definitions
      At line 185 in file startup_stm32g030xx.s
   Uses
      At line 85 in file startup_stm32g030xx.s
      At line 160 in file startup_stm32g030xx.s

FLASH_IRQHandler 00000012

Symbol: FLASH_IRQHandler
   Definitions
      At line 181 in file startup_stm32g030xx.s
   Uses
      At line 81 in file startup_stm32g030xx.s
      At line 156 in file startup_stm32g030xx.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 135 in file startup_stm32g030xx.s
   Uses
      At line 63 in file startup_stm32g030xx.s
      At line 136 in file startup_stm32g030xx.s

I2C1_IRQHandler 00000012

Symbol: I2C1_IRQHandler
   Definitions
      At line 196 in file startup_stm32g030xx.s
   Uses
      At line 101 in file startup_stm32g030xx.s
      At line 171 in file startup_stm32g030xx.s

I2C2_IRQHandler 00000012

Symbol: I2C2_IRQHandler
   Definitions
      At line 197 in file startup_stm32g030xx.s
   Uses
      At line 102 in file startup_stm32g030xx.s
      At line 172 in file startup_stm32g030xx.s

NMI_Handler 00000008

Symbol: NMI_Handler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 130 in file startup_stm32g030xx.s
   Uses
      At line 62 in file startup_stm32g030xx.s
      At line 131 in file startup_stm32g030xx.s

PendSV_Handler 0000000E

Symbol: PendSV_Handler
   Definitions
      At line 143 in file startup_stm32g030xx.s
   Uses
      At line 74 in file startup_stm32g030xx.s
      At line 144 in file startup_stm32g030xx.s

RCC_IRQHandler 00000012

Symbol: RCC_IRQHandler
   Definitions
      At line 182 in file startup_stm32g030xx.s
   Uses
      At line 82 in file startup_stm32g030xx.s
      At line 157 in file startup_stm32g030xx.s

RTC_TAMP_IRQHandler 00000012

Symbol: RTC_TAMP_IRQHandler
   Definitions
      At line 180 in file startup_stm32g030xx.s
   Uses
      At line 80 in file startup_stm32g030xx.s
      At line 155 in file startup_stm32g030xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 118 in file startup_stm32g030xx.s
   Uses
      At line 61 in file startup_stm32g030xx.s
      At line 119 in file startup_stm32g030xx.s

SPI1_IRQHandler 00000012

Symbol: SPI1_IRQHandler
   Definitions
      At line 198 in file startup_stm32g030xx.s
   Uses
      At line 103 in file startup_stm32g030xx.s
      At line 173 in file startup_stm32g030xx.s

SPI2_IRQHandler 00000012

Symbol: SPI2_IRQHandler
   Definitions
      At line 199 in file startup_stm32g030xx.s
   Uses
      At line 104 in file startup_stm32g030xx.s
      At line 174 in file startup_stm32g030xx.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


SVC_Handler 0000000C

Symbol: SVC_Handler
   Definitions
      At line 139 in file startup_stm32g030xx.s
   Uses
      At line 71 in file startup_stm32g030xx.s
      At line 140 in file startup_stm32g030xx.s

SysTick_Handler 00000010

Symbol: SysTick_Handler
   Definitions
      At line 147 in file startup_stm32g030xx.s
   Uses
      At line 75 in file startup_stm32g030xx.s
      At line 148 in file startup_stm32g030xx.s

TIM14_IRQHandler 00000012

Symbol: TIM14_IRQHandler
   Definitions
      At line 193 in file startup_stm32g030xx.s
   Uses
      At line 97 in file startup_stm32g030xx.s
      At line 168 in file startup_stm32g030xx.s

TIM16_IRQHandler 00000012

Symbol: TIM16_IRQHandler
   Definitions
      At line 194 in file startup_stm32g030xx.s
   Uses
      At line 99 in file startup_stm32g030xx.s
      At line 169 in file startup_stm32g030xx.s

TIM17_IRQHandler 00000012

Symbol: TIM17_IRQHandler
   Definitions
      At line 195 in file startup_stm32g030xx.s
   Uses
      At line 100 in file startup_stm32g030xx.s
      At line 170 in file startup_stm32g030xx.s

TIM1_BRK_UP_TRG_COM_IRQHandler 00000012

Symbol: TIM1_BRK_UP_TRG_COM_IRQHandler
   Definitions
      At line 190 in file startup_stm32g030xx.s
   Uses
      At line 91 in file startup_stm32g030xx.s
      At line 165 in file startup_stm32g030xx.s

TIM1_CC_IRQHandler 00000012

Symbol: TIM1_CC_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 191 in file startup_stm32g030xx.s
   Uses
      At line 92 in file startup_stm32g030xx.s
      At line 166 in file startup_stm32g030xx.s

TIM3_IRQHandler 00000012

Symbol: TIM3_IRQHandler
   Definitions
      At line 192 in file startup_stm32g030xx.s
   Uses
      At line 94 in file startup_stm32g030xx.s
      At line 167 in file startup_stm32g030xx.s

USART1_IRQHandler 00000012

Symbol: USART1_IRQHandler
   Definitions
      At line 200 in file startup_stm32g030xx.s
   Uses
      At line 105 in file startup_stm32g030xx.s
      At line 175 in file startup_stm32g030xx.s

USART2_IRQHandler 00000012

Symbol: USART2_IRQHandler
   Definitions
      At line 201 in file startup_stm32g030xx.s
   Uses
      At line 106 in file startup_stm32g030xx.s
      At line 176 in file startup_stm32g030xx.s

WWDG_IRQHandler 00000012

Symbol: WWDG_IRQHandler
   Definitions
      At line 179 in file startup_stm32g030xx.s
   Uses
      At line 78 in file startup_stm32g030xx.s
      At line 154 in file startup_stm32g030xx.s

31 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000100

Symbol: Heap_Size
   Definitions
      At line 43 in file startup_stm32g030xx.s
   Uses
      At line 47 in file startup_stm32g030xx.s
Comment: Heap_Size used once
Stack_Size 00000800

Symbol: Stack_Size
   Definitions
      At line 32 in file startup_stm32g030xx.s
   Uses
      At line 35 in file startup_stm32g030xx.s
Comment: Stack_Size used once
__Vectors_Size 000000C0

Symbol: __Vectors_Size
   Definitions
      At line 113 in file startup_stm32g030xx.s
   Uses
      At line 58 in file startup_stm32g030xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 121 in file startup_stm32g030xx.s
   Uses
      At line 122 in file startup_stm32g030xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 120 in file startup_stm32g030xx.s
   Uses
      At line 124 in file startup_stm32g030xx.s
Comment: __main used once
2 symbols
382 symbols in table
