Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32g030xx.o(RESET) refers to startup_stm32g030xx.o(STACK) for __initial_sp
    startup_stm32g030xx.o(RESET) refers to startup_stm32g030xx.o(.text) for Reset_Handler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.DMA1_Channel2_3_IRQHandler) for DMA1_Channel2_3_IRQHandler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.TIM1_BRK_UP_TRG_COM_IRQHandler) for TIM1_BRK_UP_TRG_COM_IRQHandler
    startup_stm32g030xx.o(RESET) refers to stm32g0xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32g030xx.o(.text) refers to system_stm32g0xx.o(i.SystemInit) for SystemInit
    startup_stm32g030xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.Blink_Leds) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.Blink_Leds) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(i.fputc) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(i.fputc) refers to usart.o(.bss) for huart1
    main.o(i.main) refers to stm32g0xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to dht11.o(i.delay_init) for delay_init
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(i.main) refers to bsp_lcd.o(i.Lcd_Init) for Lcd_Init
    main.o(i.main) refers to bsp_lcd.o(i.Lcd_Clear) for Lcd_Clear
    main.o(i.main) refers to key.o(i.KeyInit) for KeyInit
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to bsp_lcd.o(i.Gui_DrawFont_GBK16) for Gui_DrawFont_GBK16
    main.o(i.main) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    main.o(i.main) refers to esp8266.o(i.initEsp8266) for initEsp8266
    main.o(i.main) refers to dht11.o(i.FS_DHT11_Init) for FS_DHT11_Init
    main.o(i.main) refers to bsp_esp8266.o(i.ESP_TASK) for ESP_TASK
    main.o(i.main) refers to adc.o(.bss) for ADC_DMA_Value
    main.o(i.main) refers to usart.o(.bss) for huart2
    main.o(i.main) refers to esp8266.o(.bss) for StationIPV4
    gpio.o(i.D1_IN_GPIO_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.D1_OUT_GPIO_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.D1_OUT_GPIO_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.D1_OUT_GPIO_Init) refers to printf8.o(i.__0printf$8) for __2printf
    gpio.o(i.D2_IN_GPIO_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.D2_OUT_GPIO_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.AdSensor) refers to adc.o(.bss) for ADC_DMA_Value
    adc.o(i.AdSensor) refers to adc.o(.data) for AdcData_L
    adc.o(i.AdSensor_ch1) refers to adc.o(.bss) for ADC_DMA_Value
    adc.o(i.AdSensor_ch1) refers to adc.o(.data) for AdcData_L
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for hdma_adc1
    adc.o(i.MX_ADC1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC1_Init) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) for HAL_ADC_AnalogWDGConfig
    adc.o(i.MX_ADC1_Init) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for hadc1
    dma.o(i.MX_DMA_Init) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM3_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_usart2_rx
    usart.o(i.MX_USART1_UART_Init) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.MX_USART2_UART_Init) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for huart2
    stm32g0xx_it.o(i.DMA1_Channel1_IRQHandler) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g0xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32g0xx_it.o(i.DMA1_Channel2_3_IRQHandler) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g0xx_it.o(i.DMA1_Channel2_3_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) refers to memseta.o(.text) for __aeabi_memclr
    stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for Usart2type
    stm32g0xx_it.o(i.HardFault_Handler) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32g0xx_it.o(i.SysTick_Handler) refers to stm32g0xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32g0xx_it.o(i.TIM1_BRK_UP_TRG_COM_IRQHandler) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g0xx_it.o(i.TIM1_BRK_UP_TRG_COM_IRQHandler) refers to tim.o(.bss) for htim1
    stm32g0xx_it.o(i.USART2_IRQHandler) refers to stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    stm32g0xx_it.o(i.USART2_IRQHandler) refers to stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g0xx_it.o(i.USART2_IRQHandler) refers to stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32g0xx_it.o(i.USART2_IRQHandler) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    stm32g0xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    key.o(i.GetKey) refers to adc.o(i.AdSensor_ch1) for AdSensor_ch1
    key.o(i.KeyInit) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    key.o(i.KeyInit) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    key.o(i.KeyInit) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    key.o(i.KeyInit) refers to adc.o(.bss) for ADC_DMA_Value
    esp8266.o(i.ESP8266_CWQAP) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_DHCP_CUR) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_Enable_MultipleId) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp8266.o(i.ESP8266_Enable_MultipleId) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_JoinAP) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp8266.o(i.ESP8266_JoinAP) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_Link_Close) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_Link_Server) refers to memseta.o(.text) for __aeabi_memclr4
    esp8266.o(i.ESP8266_Link_Server) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp8266.o(i.ESP8266_Link_Server) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_Net_Mode_Choose) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_RESTORE) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_Read_IP) refers to _scanf_str.o(.text) for _scanf_string
    esp8266.o(i.ESP8266_Read_IP) refers to memcpya.o(.text) for __aeabi_memcpy4
    esp8266.o(i.ESP8266_Read_IP) refers to memseta.o(.text) for __aeabi_memclr4
    esp8266.o(i.ESP8266_Read_IP) refers to esp8266.o(i.reset_rxbuffClear) for reset_rxbuffClear
    esp8266.o(i.ESP8266_Read_IP) refers to strlen.o(.text) for strlen
    esp8266.o(i.ESP8266_Read_IP) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.ESP8266_Read_IP) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.ESP8266_Read_IP) refers to __0sscanf.o(.text) for __0sscanf
    esp8266.o(i.ESP8266_Read_IP) refers to esp8266.o(.constdata) for .constdata
    esp8266.o(i.ESP8266_Read_IP) refers to usart.o(.bss) for huart2
    esp8266.o(i.ESP8266_Read_IP) refers to esp8266.o(.bss) for StationIPV4
    esp8266.o(i.ESP8266_UnvarnishMode) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_UnvarnishSend) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.ESP8266_UnvarnishStop) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.cmdAT) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp8266.o(i.cmdAT) refers to esp8266.o(i.reset_rxbuffClear) for reset_rxbuffClear
    esp8266.o(i.cmdAT) refers to strlen.o(.text) for strlen
    esp8266.o(i.cmdAT) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.cmdAT) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    esp8266.o(i.cmdAT) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.cmdAT) refers to strstr.o(.text) for strstr
    esp8266.o(i.cmdAT) refers to ddiv.o(.text) for __aeabi_ddiv
    esp8266.o(i.cmdAT) refers to dfltui.o(.text) for __aeabi_ui2d
    esp8266.o(i.cmdAT) refers to cdcmple.o(.text) for __aeabi_cdcmple
    esp8266.o(i.cmdAT) refers to usart.o(.bss) for huart2
    esp8266.o(i.initEsp8266) refers to printf8.o(i.__0printf$8) for __2printf
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_RESTORE) for ESP8266_RESTORE
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.testAT) for testAT
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_Net_Mode_Choose) for ESP8266_Net_Mode_Choose
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_JoinAP) for ESP8266_JoinAP
    esp8266.o(i.initEsp8266) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_Link_Server) for ESP8266_Link_Server
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_Enable_MultipleId) for ESP8266_Enable_MultipleId
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_Read_IP) for ESP8266_Read_IP
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_UnvarnishMode) for ESP8266_UnvarnishMode
    esp8266.o(i.initEsp8266) refers to esp8266.o(i.ESP8266_UnvarnishSend) for ESP8266_UnvarnishSend
    esp8266.o(i.reset_rxbuffClear) refers to printf8.o(i.__0printf$8) for __2printf
    esp8266.o(i.reset_rxbuffClear) refers to strlen.o(.text) for strlen
    esp8266.o(i.reset_rxbuffClear) refers to memseta.o(.text) for __aeabi_memclr
    esp8266.o(i.reset_rxbuffClear) refers to usart.o(.bss) for Usart2type
    esp8266.o(i.testAT) refers to printf8.o(i.__0printf$8) for __2printf
    esp8266.o(i.testAT) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.testAT) refers to esp8266.o(i.cmdAT) for cmdAT
    esp8266.o(i.testAT) refers to usart.o(.bss) for huart2
    dht11.o(i.DHT11_Check) refers to dht11.o(i.DHT11_IO_IN) for DHT11_IO_IN
    dht11.o(i.DHT11_Check) refers to dht11.o(i.delay_us) for delay_us
    dht11.o(i.DHT11_Check) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    dht11.o(i.DHT11_IO_IN) refers to memseta.o(.text) for __aeabi_memclr4
    dht11.o(i.DHT11_IO_IN) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.DHT11_IO_OUT) refers to memseta.o(.text) for __aeabi_memclr4
    dht11.o(i.DHT11_IO_OUT) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.DHT11_Read_Bit) refers to dht11.o(i.delay_us) for delay_us
    dht11.o(i.DHT11_Read_Bit) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    dht11.o(i.DHT11_Read_Byte) refers to dht11.o(i.DHT11_Read_Bit) for DHT11_Read_Bit
    dht11.o(i.DHT11_Read_Data) refers to dht11.o(i.DHT11_Rst) for DHT11_Rst
    dht11.o(i.DHT11_Read_Data) refers to dht11.o(i.DHT11_Check) for DHT11_Check
    dht11.o(i.DHT11_Read_Data) refers to dht11.o(i.DHT11_Read_Byte) for DHT11_Read_Byte
    dht11.o(i.DHT11_Read_Data) refers to dht11.o(.data) for ucharRH_data_L
    dht11.o(i.DHT11_Rst) refers to dht11.o(i.DHT11_IO_OUT) for DHT11_IO_OUT
    dht11.o(i.DHT11_Rst) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    dht11.o(i.DHT11_Rst) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    dht11.o(i.DHT11_Rst) refers to dht11.o(i.delay_us) for delay_us
    dht11.o(i.FS_DHT11_Init) refers to memseta.o(.text) for __aeabi_memclr4
    dht11.o(i.FS_DHT11_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.FS_DHT11_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    dht11.o(i.FS_DHT11_Init) refers to dht11.o(i.DHT11_Rst) for DHT11_Rst
    dht11.o(i.FS_DHT11_Init) refers to dht11.o(i.DHT11_Check) for DHT11_Check
    dht11.o(i.delay_init) refers to dht11.o(.data) for fac_us
    dht11.o(i.delay_ms) refers to dht11.o(i.delay_us) for delay_us
    dht11.o(i.delay_us) refers to dht11.o(.data) for fac_us
    stm32g0xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Rising_Callback) for HAL_GPIO_EXTI_Rising_Callback
    stm32g0xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Falling_Callback) for HAL_GPIO_EXTI_Falling_Callback
    stm32g0xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g0xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g0xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g0xx_hal_adc.o(i.ADC_DMAError) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g0xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32g0xx_hal_adc.o(i.ADC_Disable) refers to stm32g0xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g0xx_hal_adc.o(i.ADC_Disable) refers to stm32g0xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g0xx_hal_adc.o(i.ADC_Disable) refers to stm32g0xx_hal_adc.o(i.LL_ADC_Disable) for LL_ADC_Disable
    stm32g0xx_hal_adc.o(i.ADC_Disable) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_adc.o(i.ADC_Enable) refers to stm32g0xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g0xx_hal_adc.o(i.ADC_Enable) refers to stm32g0xx_hal_adc.o(i.LL_ADC_Enable) for LL_ADC_Enable
    stm32g0xx_hal_adc.o(i.ADC_Enable) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g0xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g0xx_hal_adc.o(i.LL_ADC_GetCommonPathInternalCh) for LL_ADC_GetCommonPathInternalCh
    stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g0xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh) for LL_ADC_SetCommonPathInternalCh
    stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_ChannelConfigReadyCallback) for HAL_ADCEx_ChannelConfigReadyCallback
    stm32g0xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32g0xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g0xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled) for LL_ADC_IsInternalRegulatorEnabled
    stm32g0xx_hal_adc.o(i.HAL_ADC_Init) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g0xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g0xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g0xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonChannels) for LL_ADC_SetSamplingTimeCommonChannels
    stm32g0xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g0xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.LL_ADC_Disable) for LL_ADC_Disable
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g0xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g0xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g0xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g0xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g0xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g0xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g0xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g0xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32g0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32g0xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator) refers to stm32g0xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g0xx.o(.constdata) for AHBPrescTable
    stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g0xx_hal.o(.data) for uwTickPrio
    stm32g0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g0xx_hal.o(.data) for uwTickPrio
    stm32g0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32g0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g0xx.o(.constdata) for APBPrescTable
    stm32g0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g0xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g0xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_LSECSSCallback) for HAL_RCC_LSECSSCallback
    stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g0xx_hal.o(.data) for uwTickPrio
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g0xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g0xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g0xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g0xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g0xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g0xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g0xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) for FLASH_OB_WRPConfig
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_OB_OptrConfig) for FLASH_OB_OptrConfig
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g0xx_hal_flash.o(.bss) for pFlash
    stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_dma.o(i.HAL_DMA_DeInit) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g0xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g0xx_hal_dma.o(i.HAL_DMA_Init) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g0xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g0xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32g0xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32g0xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g0xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback) refers to stm32g0xx_hal_dma.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32g0xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g0xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown) refers to stm32g0xx_hal_dma.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp) refers to stm32g0xx_hal_dma.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown) refers to stm32g0xx_hal_dma.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp) refers to stm32g0xx_hal_dma.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g0xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32g0xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32g0xx_hal.o(i.HAL_DeInit) refers to stm32g0xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32g0xx_hal.o(i.HAL_Delay) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal.o(i.HAL_Delay) refers to stm32g0xx_hal.o(.data) for uwTickFreq
    stm32g0xx_hal.o(i.HAL_GetTick) refers to stm32g0xx_hal.o(.data) for uwTick
    stm32g0xx_hal.o(i.HAL_GetTickFreq) refers to stm32g0xx_hal.o(.data) for uwTickFreq
    stm32g0xx_hal.o(i.HAL_GetTickPrio) refers to stm32g0xx_hal.o(.data) for uwTickPrio
    stm32g0xx_hal.o(i.HAL_IncTick) refers to stm32g0xx_hal.o(.data) for uwTick
    stm32g0xx_hal.o(i.HAL_Init) refers to stm32g0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal.o(i.HAL_Init) refers to stm32g0xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32g0xx_hal.o(i.HAL_InitTick) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal.o(i.HAL_InitTick) refers to stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g0xx_hal.o(i.HAL_InitTick) refers to stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g0xx_hal.o(i.HAL_InitTick) refers to stm32g0xx_hal.o(.data) for uwTickFreq
    stm32g0xx_hal.o(i.HAL_InitTick) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    stm32g0xx_hal.o(i.HAL_SetTickFreq) refers to stm32g0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g0xx_hal.o(i.HAL_SetTickFreq) refers to stm32g0xx_hal.o(.data) for uwTickFreq
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g0xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g0xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g0xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g0xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32g0xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to app_system.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g0xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32g0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g0xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32g0xx_hal_tim.o(i.TIM_DMAError) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to app_system.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32g0xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32g0xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32g0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g0xx_hal_dma.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32g0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g0xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g0xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g0xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g0xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32g0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g0xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g0xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g0xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g0xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g0xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g0xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32g0xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g0xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g0xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g0xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g0xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g0xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g0xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g0xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g0xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g0xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g0xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g0xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32g0xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g0xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32g0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g0xx_hal_uart.o(i.UART_DMAError) refers to stm32g0xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g0xx_hal_uart.o(i.UART_DMAError) refers to stm32g0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g0xx_hal_uart.o(i.UART_DMAError) refers to stm32g0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g0xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g0xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32g0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g0xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32g0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g0xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32g0xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g0xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32g0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g0xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32g0xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32g0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g0xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32g0xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g0xx_it.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g0xx_hal_uart.o(i.UART_SetConfig) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g0xx_hal_uart.o(i.UART_SetConfig) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32g0xx_hal_uart.o(i.UART_SetConfig) refers to stm32g0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32g0xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32g0xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32g0xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g0xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig) for UARTEx_Wakeup_AddressConfig
    stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g0xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to idiv.o(.text) for __aeabi_idivmod
    system_stm32g0xx.o(i.SystemCoreClockUpdate) refers to uidiv.o(.text) for __aeabi_uidivmod
    system_stm32g0xx.o(i.SystemCoreClockUpdate) refers to system_stm32g0xx.o(.data) for SystemCoreClock
    system_stm32g0xx.o(i.SystemCoreClockUpdate) refers to system_stm32g0xx.o(.constdata) for AHBPrescTable
    fs_crc.o(i.usMBCRC16) refers to fs_crc.o(.constdata) for aucCRCHi
    fs_protocol.o(i.analysis_Packet) refers to fs_crc.o(i.crc8) for crc8
    fs_protocol.o(i.analysis_Packet) refers to printf8.o(i.__0printf$8) for __2printf
    fs_protocol.o(i.analysis_Packet) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    fs_protocol.o(i.analysis_Packet) refers to bsp_beep.o(i.set_Beep_Status) for set_Beep_Status
    fs_protocol.o(i.analysis_Packet) refers to main.o(i.Blink_Leds) for Blink_Leds
    fs_protocol.o(i.analysis_Packet) refers to bsp_p9813.o(i.rgb_setValue) for rgb_setValue
    fs_protocol.o(i.analysis_Packet) refers to app_system.o(.data) for global_Data
    fs_protocol.o(i.packet_data) refers to fs_crc.o(i.crc8) for crc8
    fs_protocol.o(i.packet_data) refers to app_system.o(.data) for global_Data
    bsp_esp8266.o(i.ATRecv) refers to printf8.o(i.__0printf$8) for __2printf
    bsp_esp8266.o(i.ATRecv) refers to strstr.o(.text) for strstr
    bsp_esp8266.o(i.ATRecv) refers to strtok.o(.text) for strtok
    bsp_esp8266.o(i.ATRecv) refers to bsp_lcd.o(i.Gui_DrawFont_GBK16) for Gui_DrawFont_GBK16
    bsp_esp8266.o(i.ATRecv) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_esp8266.o(i.ATRecv) refers to memseta.o(.text) for __aeabi_memclr
    bsp_esp8266.o(i.ATRecv) refers to usart.o(.bss) for Usart2type
    bsp_esp8266.o(i.ATRecv) refers to bsp_esp8266.o(.data) for ATRecCmdNum
    bsp_esp8266.o(i.ATSend) refers to memseta.o(.text) for __aeabi_memclr
    bsp_esp8266.o(i.ATSend) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    bsp_esp8266.o(i.ATSend) refers to strlen.o(.text) for strlen
    bsp_esp8266.o(i.ATSend) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    bsp_esp8266.o(i.ATSend) refers to printf8.o(i.__0printf$8) for __2printf
    bsp_esp8266.o(i.ATSend) refers to dfixui.o(.text) for __aeabi_d2uiz
    bsp_esp8266.o(i.ATSend) refers to bsp_softwaretimer.o(i.setTime) for setTime
    bsp_esp8266.o(i.ATSend) refers to usart.o(.bss) for Usart2type
    bsp_esp8266.o(i.ATSend) refers to bsp_esp8266.o(.data) for ATCmds
    bsp_esp8266.o(i.ESP8266_Init) refers to printf8.o(i.__0printf$8) for __2printf
    bsp_esp8266.o(i.ESP8266_Init) refers to bsp_lcd.o(i.Gui_DrawFont_GBK16) for Gui_DrawFont_GBK16
    bsp_esp8266.o(i.ESP8266_Init) refers to bsp_esp8266.o(.data) for ESP_TaskStatus
    bsp_esp8266.o(i.ESP_TASK) refers to bsp_esp8266.o(i.SEND_TASK) for SEND_TASK
    bsp_esp8266.o(i.ESP_TASK) refers to bsp_esp8266.o(i.ATSend) for ATSend
    bsp_esp8266.o(i.ESP_TASK) refers to bsp_esp8266.o(i.ATRecv) for ATRecv
    bsp_esp8266.o(i.ESP_TASK) refers to bsp_esp8266.o(i.Rec_WaitAT) for Rec_WaitAT
    bsp_esp8266.o(i.ESP_TASK) refers to bsp_esp8266.o(.data) for ESP_TaskStatus
    bsp_esp8266.o(i.Rec_WaitAT) refers to stm32g0xx_hal_dma.o(i.__ARM_common_switch8) for __ARM_common_switch8
    bsp_esp8266.o(i.Rec_WaitAT) refers to printf8.o(i.__0printf$8) for __2printf
    bsp_esp8266.o(i.Rec_WaitAT) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_esp8266.o(i.Rec_WaitAT) refers to bsp_softwaretimer.o(i.setTime) for setTime
    bsp_esp8266.o(i.Rec_WaitAT) refers to bsp_softwaretimer.o(i.compareTime) for compareTime
    bsp_esp8266.o(i.Rec_WaitAT) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    bsp_esp8266.o(i.Rec_WaitAT) refers to bsp_esp8266.o(.data) for ATCurrentCmdNum
    bsp_esp8266.o(i.SEND_TASK) refers to main.o(i.Blink_Leds) for Blink_Leds
    bsp_esp8266.o(i.SEND_TASK) refers to app_system.o(i.get_SensorData) for get_SensorData
    bsp_esp8266.o(i.SEND_TASK) refers to app_system.o(i.DisplayDeviceData) for DisplayDeviceData
    bsp_esp8266.o(i.SEND_TASK) refers to memseta.o(.text) for __aeabi_memclr
    bsp_esp8266.o(i.SEND_TASK) refers to fs_protocol.o(i.packet_data) for packet_data
    bsp_esp8266.o(i.SEND_TASK) refers to printf8.o(i.__0printf$8) for __2printf
    bsp_esp8266.o(i.SEND_TASK) refers to stm32g0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    bsp_esp8266.o(i.SEND_TASK) refers to fs_protocol.o(i.analysis_Packet) for analysis_Packet
    bsp_esp8266.o(i.SEND_TASK) refers to app_system.o(i.ExecuteCommand) for ExecuteCommand
    bsp_esp8266.o(i.SEND_TASK) refers to bsp_esp8266.o(.data) for ESP_TaskStatus
    bsp_esp8266.o(i.SEND_TASK) refers to app_system.o(.data) for global_Data
    bsp_esp8266.o(i.SEND_TASK) refers to bsp_esp8266.o(.bss) for SendBuff
    bsp_esp8266.o(i.SEND_TASK) refers to usart.o(.bss) for huart2
    bsp_esp8266.o(.data) refers to bsp_esp8266.o(.conststring) for .conststring
    bsp_lcd.o(i.Gui_DrawFont_GBK16) refers to bsp_lcd.o(i.Gui_DrawPoint) for Gui_DrawPoint
    bsp_lcd.o(i.Gui_DrawFont_GBK16) refers to strlen.o(.text) for strlen
    bsp_lcd.o(i.Gui_DrawFont_GBK16) refers to bsp_lcd.o(.constdata) for Zk_ASCII8X16
    bsp_lcd.o(i.Gui_DrawPoint) refers to bsp_lcd.o(i.Lcd_SetRegion) for Lcd_SetRegion
    bsp_lcd.o(i.Gui_DrawPoint) refers to bsp_lcd.o(i.LCD_WriteData_16Bit) for LCD_WriteData_16Bit
    bsp_lcd.o(i.LCD_WriteData_16Bit) refers to bsp_lcd.o(i.SPI_WriteData) for SPI_WriteData
    bsp_lcd.o(i.Lcd_Clear) refers to bsp_lcd.o(i.Lcd_SetRegion) for Lcd_SetRegion
    bsp_lcd.o(i.Lcd_Clear) refers to bsp_lcd.o(i.Lcd_WriteIndex) for Lcd_WriteIndex
    bsp_lcd.o(i.Lcd_Clear) refers to bsp_lcd.o(i.LCD_WriteData_16Bit) for LCD_WriteData_16Bit
    bsp_lcd.o(i.Lcd_ClearLine) refers to bsp_lcd.o(i.Lcd_SetRegion) for Lcd_SetRegion
    bsp_lcd.o(i.Lcd_ClearLine) refers to bsp_lcd.o(i.Lcd_WriteIndex) for Lcd_WriteIndex
    bsp_lcd.o(i.Lcd_ClearLine) refers to bsp_lcd.o(i.LCD_WriteData_16Bit) for LCD_WriteData_16Bit
    bsp_lcd.o(i.Lcd_Init) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    bsp_lcd.o(i.Lcd_Init) refers to bsp_lcd.o(i.Lcd_WriteIndex) for Lcd_WriteIndex
    bsp_lcd.o(i.Lcd_Init) refers to bsp_lcd.o(i.Lcd_WriteData) for Lcd_WriteData
    bsp_lcd.o(i.Lcd_SetRegion) refers to bsp_lcd.o(i.Lcd_WriteIndex) for Lcd_WriteIndex
    bsp_lcd.o(i.Lcd_SetRegion) refers to bsp_lcd.o(i.Lcd_WriteData) for Lcd_WriteData
    bsp_lcd.o(i.Lcd_SetXY) refers to bsp_lcd.o(i.Lcd_SetRegion) for Lcd_SetRegion
    bsp_lcd.o(i.Lcd_ShowString) refers to bsp_lcd.o(i.Lcd_ClearLine) for Lcd_ClearLine
    bsp_lcd.o(i.Lcd_ShowString) refers to bsp_lcd.o(i.Gui_DrawFont_GBK16) for Gui_DrawFont_GBK16
    bsp_lcd.o(i.Lcd_WriteData) refers to bsp_lcd.o(i.SPI_WriteData) for SPI_WriteData
    bsp_lcd.o(i.Lcd_WriteIndex) refers to bsp_lcd.o(i.SPI_WriteData) for SPI_WriteData
    bsp_lcd.o(i.Lcd_WriteReg) refers to bsp_lcd.o(i.Lcd_WriteIndex) for Lcd_WriteIndex
    bsp_lcd.o(i.Lcd_WriteReg) refers to bsp_lcd.o(i.Lcd_WriteData) for Lcd_WriteData
    bsp_lcd.o(i.showimage) refers to bsp_lcd.o(i.Lcd_SetRegion) for Lcd_SetRegion
    bsp_lcd.o(i.showimage) refers to bsp_lcd.o(i.LCD_WriteData_16Bit) for LCD_WriteData_16Bit
    bsp_lcd.o(i.showimage_farsight) refers to bsp_lcd.o(i.Lcd_SetRegion) for Lcd_SetRegion
    bsp_lcd.o(i.showimage_farsight) refers to bsp_lcd.o(i.LCD_WriteData_16Bit) for LCD_WriteData_16Bit
    bsp_p9813.o(i.RGB_Send_Data) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_p9813.o(i.RGB_Send_Data) refers to dht11.o(i.delay_us) for delay_us
    bsp_p9813.o(i.rgb_Gpio_Init) refers to memseta.o(.text) for __aeabi_memclr4
    bsp_p9813.o(i.rgb_Gpio_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_p9813.o(i.rgb_Gpio_Init) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    bsp_p9813.o(i.rgb_setValue) refers to bsp_p9813.o(i.Color_Data) for Color_Data
    bsp_p9813.o(i.rgb_setValue) refers to bsp_p9813.o(i.RGB_Send_Data) for RGB_Send_Data
    bsp_softwaretimer.o(i.compareTime) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_softwaretimer.o(i.setTime) refers to stm32g0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_ultrasonic.o(i.get_ultrasonic_val) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_ultrasonic.o(i.get_ultrasonic_val) refers to dht11.o(i.delay_us) for delay_us
    bsp_ultrasonic.o(i.get_ultrasonic_val) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    bsp_ultrasonic.o(i.get_ultrasonic_val) refers to uidiv.o(.text) for __aeabi_uidivmod
    bsp_ultrasonic.o(i.get_ultrasonic_val) refers to tim.o(.bss) for htim3
    bsp_ultrasonic.o(i.ultrasonicUsing_Init) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    bsp_ultrasonic.o(i.ultrasonicUsing_Init) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    app_system.o(i.BSP_Init) refers to app_system.o(i.Read_ADC_CoreType) for Read_ADC_CoreType
    app_system.o(i.BSP_Init) refers to printf8.o(i.__0printf$8) for __2printf
    app_system.o(i.BSP_Init) refers to gpio.o(i.D1_OUT_GPIO_Init) for D1_OUT_GPIO_Init
    app_system.o(i.BSP_Init) refers to gpio.o(i.D2_OUT_GPIO_Init) for D2_OUT_GPIO_Init
    app_system.o(i.BSP_Init) refers to gpio.o(i.D2_IN_GPIO_Init) for D2_IN_GPIO_Init
    app_system.o(i.BSP_Init) refers to bsp_ultrasonic.o(i.ultrasonicUsing_Init) for ultrasonicUsing_Init
    app_system.o(i.BSP_Init) refers to gpio.o(i.D1_IN_GPIO_Init) for D1_IN_GPIO_Init
    app_system.o(i.BSP_Init) refers to app_system.o(.data) for global_Data
    app_system.o(i.DisplayDeviceData) refers to memseta.o(.text) for __aeabi_memclr4
    app_system.o(i.DisplayDeviceData) refers to bsp_lcd.o(i.Lcd_ClearLine) for Lcd_ClearLine
    app_system.o(i.DisplayDeviceData) refers to bsp_lcd.o(i.Gui_DrawFont_GBK16) for Gui_DrawFont_GBK16
    app_system.o(i.DisplayDeviceData) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    app_system.o(i.DisplayDeviceData) refers to app_system.o(.data) for global_Data
    app_system.o(i.DisplayDeviceLogo) refers to bsp_lcd.o(i.Lcd_Init) for Lcd_Init
    app_system.o(i.DisplayDeviceLogo) refers to bsp_lcd.o(i.Lcd_Clear) for Lcd_Clear
    app_system.o(i.DisplayDeviceLogo) refers to bsp_lcd.o(i.Gui_DrawFont_GBK16) for Gui_DrawFont_GBK16
    app_system.o(i.DisplayDeviceLogo) refers to app_system.o(.data) for global_Data
    app_system.o(i.ExecuteCommand) refers to strstr.o(.text) for strstr
    app_system.o(i.ExecuteCommand) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    app_system.o(i.ExecuteCommand) refers to bsp_beep.o(i.set_Beep_Status) for set_Beep_Status
    app_system.o(i.ExecuteCommand) refers to app_system.o(i.DisplayDeviceData) for DisplayDeviceData
    app_system.o(i.HAL_TIM_PeriodElapsedCallback) refers to app_system.o(.data) for global_Data
    app_system.o(i.Read_ADC_CoreType) refers to stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    app_system.o(i.Read_ADC_CoreType) refers to stm32g0xx_hal.o(i.HAL_Delay) for HAL_Delay
    app_system.o(i.Read_ADC_CoreType) refers to printf8.o(i.__0printf$8) for __2printf
    app_system.o(i.Read_ADC_CoreType) refers to app_system.o(.bss) for ADC_DMA_Value_Buff
    app_system.o(i.Read_ADC_CoreType) refers to adc.o(.bss) for hadc1
    app_system.o(i.get_SensorData) refers to uidiv.o(.text) for __aeabi_uidivmod
    app_system.o(i.get_SensorData) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    app_system.o(i.get_SensorData) refers to dht11.o(i.DHT11_Read_Data) for DHT11_Read_Data
    app_system.o(i.get_SensorData) refers to bsp_ultrasonic.o(i.get_ultrasonic_val) for get_ultrasonic_val
    app_system.o(i.get_SensorData) refers to app_system.o(.bss) for ADC_DMA_Value_Buff
    app_system.o(i.get_SensorData) refers to app_system.o(.data) for global_Data
    bsp_beep.o(i.set_Beep_Status) refers to stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    idiv.o(.text) refers to uidiv.o(.text) for __aeabi_uidivmod
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32g030xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32g030xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32g030xx.o(HEAP), (256 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(i.D1_IN_GPIO_Init), (36 bytes).
    Removing gpio.o(i.D1_OUT_GPIO_Init), (96 bytes).
    Removing gpio.o(i.D2_IN_GPIO_Init), (36 bytes).
    Removing gpio.o(i.D2_OUT_GPIO_Init), (40 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(i.AdSensor), (44 bytes).
    Removing adc.o(i.AdSensor_ch1), (44 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (52 bytes).
    Removing adc.o(.data), (2 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (100 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspInit), (156 bytes).
    Removing tim.o(i.HAL_TIM_MspPostInit), (2 bytes).
    Removing tim.o(i.MX_TIM17_Init), (2 bytes).
    Removing tim.o(i.MX_TIM1_Init), (160 bytes).
    Removing tim.o(i.MX_TIM3_Init), (120 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (104 bytes).
    Removing stm32g0xx_it.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_it.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(i.GetKey), (96 bytes).
    Removing esp8266.o(.rev16_text), (4 bytes).
    Removing esp8266.o(.revsh_text), (4 bytes).
    Removing esp8266.o(i.ESP8266_CWQAP), (44 bytes).
    Removing esp8266.o(i.ESP8266_DHCP_CUR), (52 bytes).
    Removing esp8266.o(i.ESP8266_Link_Close), (44 bytes).
    Removing esp8266.o(i.ESP8266_UnvarnishStop), (84 bytes).
    Removing dht11.o(.rev16_text), (4 bytes).
    Removing dht11.o(.revsh_text), (4 bytes).
    Removing dht11.o(i.delay_ms), (24 bytes).
    Removing stm32g0xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_gpio.o(i.HAL_GPIO_DeInit), (268 bytes).
    Removing stm32g0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Falling_Callback), (2 bytes).
    Removing stm32g0xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (52 bytes).
    Removing stm32g0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Rising_Callback), (2 bytes).
    Removing stm32g0xx_hal_gpio.o(i.HAL_GPIO_LockPin), (50 bytes).
    Removing stm32g0xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32g0xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_adc.o(i.ADC_ConversionStop), (100 bytes).
    Removing stm32g0xx_hal_adc.o(i.ADC_Disable), (126 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_DeInit), (184 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_GetState), (6 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_GetValue), (8 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_IRQHandler), (424 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_PollForConversion), (210 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_PollForEvent), (218 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_Start), (112 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_Start_IT), (176 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_Stop), (78 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (144 bytes).
    Removing stm32g0xx_hal_adc.o(i.HAL_ADC_Stop_IT), (90 bytes).
    Removing stm32g0xx_hal_adc.o(i.LL_ADC_IsDisableOngoing), (12 bytes).
    Removing stm32g0xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (14 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (120 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (200 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_ChannelConfigReadyCallback), (2 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (40 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32g0xx_hal_adc_ex.o(i.LL_ADC_IsEnabled), (16 bytes).
    Removing stm32g0xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_DeInit), (208 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_DisableLSECSS), (20 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_EnableLSECSS), (20 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (252 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_LSECSSCallback), (2 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (96 bytes).
    Removing stm32g0xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (48 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (120 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (188 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (68 bytes).
    Removing stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (600 bytes).
    Removing stm32g0xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_flash.o(i.FLASH_Program_DoubleWord), (40 bytes).
    Removing stm32g0xx_hal_flash.o(i.FLASH_Program_Fast), (84 bytes).
    Removing stm32g0xx_hal_flash.o(i.FLASH_WaitForLastOperation), (144 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (300 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_Lock), (36 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (36 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (52 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_Program), (120 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_Program_IT), (140 bytes).
    Removing stm32g0xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32g0xx_hal_flash.o(.bss), (24 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_FlushCaches), (72 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_MassErase), (24 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (24 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (64 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_OB_OptrConfig), (32 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig), (32 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (124 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_FlashEmptyCheck), (16 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_ForceFlashEmpty), (28 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (44 bytes).
    Removing stm32g0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32g0xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_dma.o(i.HAL_DMA_DeInit), (160 bytes).
    Removing stm32g0xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32g0xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (364 bytes).
    Removing stm32g0xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (92 bytes).
    Removing stm32g0xx_hal_dma.o(i.HAL_DMA_Start), (120 bytes).
    Removing stm32g0xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (108 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (74 bytes).
    Removing stm32g0xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (90 bytes).
    Removing stm32g0xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (38 bytes).
    Removing stm32g0xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (38 bytes).
    Removing stm32g0xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (120 bytes).
    Removing stm32g0xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_DeInit), (32 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (40 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (92 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (44 bytes).
    Removing stm32g0xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (80 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (16 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (104 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (104 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (80 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (32 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (16 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (152 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (152 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (20 bytes).
    Removing stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32g0xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (88 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_MPU_Enable), (48 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (60 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (72 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (56 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (36 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g0xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32g0xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32g0xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32g0xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32g0xx_hal.o(i.HAL_DeInit), (52 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32g0xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32g0xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32g0xx_hal.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SYSCFG_DisableClampingDiode), (16 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (20 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SYSCFG_DisableRemap), (16 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SYSCFG_EnableClampingDiode), (16 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (20 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SYSCFG_EnableRemap), (16 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SetTickFreq), (44 bytes).
    Removing stm32g0xx_hal.o(i.HAL_SuspendTick), (20 bytes).
    Removing stm32g0xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (160 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_ClearPending), (56 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (40 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (224 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_GetHandle), (16 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_GetPending), (64 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (96 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (42 bytes).
    Removing stm32g0xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (260 bytes).
    Removing stm32g0xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Init), (56 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Start), (56 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (148 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (56 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Stop), (64 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (64 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (272 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (420 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (32 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (520 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (166 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (520 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (166 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (192 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (396 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (128 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (112 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (192 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (160 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (226 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Init), (56 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start), (56 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (368 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (132 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Stop), (64 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (180 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (144 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (150 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Init), (56 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start), (116 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (428 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (192 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Stop), (152 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (268 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (232 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (308 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (92 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (116 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (164 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (188 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (386 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Init), (56 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start), (116 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (428 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (192 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (160 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (268 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (232 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (52 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (112 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (112 bytes).
    Removing stm32g0xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_Base_SetConfig), (144 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_CCxChannelCmd), (34 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMACaptureCplt), (70 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (70 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (70 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (70 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMAError), (20 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (20 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (20 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_ETR_SetConfig), (28 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_ITRx_SetConfig), (24 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_OC1_SetConfig), (156 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_OC2_SetConfig), (172 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_OC3_SetConfig), (168 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_OC4_SetConfig), (116 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_OC5_SetConfig), (104 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_OC6_SetConfig), (104 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (180 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (50 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_TI1_SetConfig), (96 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (50 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_TI2_SetConfig), (72 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_TI3_SetConfig), (70 bytes).
    Removing stm32g0xx_hal_tim.o(i.TIM_TI4_SetConfig), (78 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (256 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (172 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (144 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (172 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (144 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_DisarmBreakInput), (104 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (72 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (220 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (56 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (160 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (68 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (76 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (120 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (72 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (316 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (140 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (108 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (196 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (196 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (34 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (58 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (108 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (132 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (72 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (316 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (140 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (108 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (196 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (196 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput), (136 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (54 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_TISelection), (92 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32g0xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32g0xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (74 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (74 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_HalfDuplex_Init), (132 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_LIN_Init), (174 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (60 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (60 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (14 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_MultiProcessor_Init), (164 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_Abort), (232 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceive), (132 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (164 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmit), (132 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (160 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_Abort_IT), (288 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_DMAPause), (126 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_DMAResume), (110 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_DeInit), (76 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_GetError), (8 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_GetState), (16 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_Receive), (360 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_Receive_IT), (376 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (224 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_Transmit_IT), (232 bytes).
    Removing stm32g0xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_DMARxAbortCallback), (80 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (44 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_DMATransmitCplt), (58 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_DMATxAbortCallback), (90 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (48 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT), (112 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN), (188 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT), (112 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN), (184 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_TxISR_16BIT), (74 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (106 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_TxISR_8BIT), (70 bytes).
    Removing stm32g0xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (100 bytes).
    Removing stm32g0xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32g0xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32g0xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (66 bytes).
    Removing stm32g0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (160 bytes).
    Removing stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (50 bytes).
    Removing stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (86 bytes).
    Removing stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (50 bytes).
    Removing stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (152 bytes).
    Removing stm32g0xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig), (42 bytes).
    Removing system_stm32g0xx.o(.rev16_text), (4 bytes).
    Removing system_stm32g0xx.o(.revsh_text), (4 bytes).
    Removing system_stm32g0xx.o(i.SystemCoreClockUpdate), (236 bytes).
    Removing fs_crc.o(i.usMBCRC16), (52 bytes).
    Removing fs_crc.o(.constdata), (512 bytes).
    Removing fs_protocol.o(.rev16_text), (4 bytes).
    Removing fs_protocol.o(.revsh_text), (4 bytes).
    Removing bsp_esp8266.o(.rev16_text), (4 bytes).
    Removing bsp_esp8266.o(.revsh_text), (4 bytes).
    Removing bsp_esp8266.o(i.ESP8266_Init), (92 bytes).
    Removing bsp_lcd.o(.rev16_text), (4 bytes).
    Removing bsp_lcd.o(.revsh_text), (4 bytes).
    Removing bsp_lcd.o(i.Delay_ms), (26 bytes).
    Removing bsp_lcd.o(i.Lcd_SetXY), (20 bytes).
    Removing bsp_lcd.o(i.Lcd_ShowString), (40 bytes).
    Removing bsp_lcd.o(i.Lcd_WriteReg), (40 bytes).
    Removing bsp_lcd.o(i.showimage), (52 bytes).
    Removing bsp_lcd.o(i.showimage_farsight), (52 bytes).
    Removing bsp_p9813.o(.rev16_text), (4 bytes).
    Removing bsp_p9813.o(.revsh_text), (4 bytes).
    Removing bsp_p9813.o(i.rgb_Gpio_Init), (88 bytes).
    Removing bsp_softwaretimer.o(.rev16_text), (4 bytes).
    Removing bsp_softwaretimer.o(.revsh_text), (4 bytes).
    Removing bsp_ultrasonic.o(.rev16_text), (4 bytes).
    Removing bsp_ultrasonic.o(.revsh_text), (4 bytes).
    Removing bsp_ultrasonic.o(i.ultrasonicUsing_Init), (12 bytes).
    Removing app_system.o(.rev16_text), (4 bytes).
    Removing app_system.o(.revsh_text), (4 bytes).
    Removing app_system.o(i.BSP_Init), (1212 bytes).
    Removing app_system.o(i.DisplayDeviceLogo), (660 bytes).
    Removing app_system.o(i.Read_ADC_CoreType), (76 bytes).
    Removing bsp_beep.o(.rev16_text), (4 bytes).
    Removing bsp_beep.o(.revsh_text), (4 bytes).
    Removing dadd.o(.text), (356 bytes).
    Removing dmul.o(.text), (208 bytes).
    Removing dfixul.o(.text), (64 bytes).
    Removing cdrcmple.o(.text), (40 bytes).

481 unused section(s) (total 38890 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal.c 0x00000000   Number         0  stm32g0xx_hal.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_adc.c 0x00000000   Number         0  stm32g0xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_adc_ex.c 0x00000000   Number         0  stm32g0xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_cortex.c 0x00000000   Number         0  stm32g0xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_dma.c 0x00000000   Number         0  stm32g0xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_dma_ex.c 0x00000000   Number         0  stm32g0xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_exti.c 0x00000000   Number         0  stm32g0xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_flash.c 0x00000000   Number         0  stm32g0xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_flash_ex.c 0x00000000   Number         0  stm32g0xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_gpio.c 0x00000000   Number         0  stm32g0xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pwr.c 0x00000000   Number         0  stm32g0xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g0xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_rcc.c 0x00000000   Number         0  stm32g0xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g0xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_tim.c 0x00000000   Number         0  stm32g0xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_tim_ex.c 0x00000000   Number         0  stm32g0xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_uart.c 0x00000000   Number         0  stm32g0xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_uart_ex.c 0x00000000   Number         0  stm32g0xx_hal_uart_ex.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_ll_adc.c 0x00000000   Number         0  stm32g0xx_ll_adc.o ABSOLUTE
    ../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_ll_rcc.c 0x00000000   Number         0  stm32g0xx_ll_rcc.o ABSOLUTE
    ../Src/adc.c                             0x00000000   Number         0  adc.o ABSOLUTE
    ../Src/dma.c                             0x00000000   Number         0  dma.o ABSOLUTE
    ../Src/gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ../Src/main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ../Src/stm32g0xx_hal_msp.c               0x00000000   Number         0  stm32g0xx_hal_msp.o ABSOLUTE
    ../Src/stm32g0xx_it.c                    0x00000000   Number         0  stm32g0xx_it.o ABSOLUTE
    ../Src/system_stm32g0xx.c                0x00000000   Number         0  system_stm32g0xx.o ABSOLUTE
    ../Src/tim.c                             0x00000000   Number         0  tim.o ABSOLUTE
    ../Src/usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal.c 0x00000000   Number         0  stm32g0xx_hal.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_adc.c 0x00000000   Number         0  stm32g0xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_adc_ex.c 0x00000000   Number         0  stm32g0xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_cortex.c 0x00000000   Number         0  stm32g0xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_dma.c 0x00000000   Number         0  stm32g0xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_dma_ex.c 0x00000000   Number         0  stm32g0xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_exti.c 0x00000000   Number         0  stm32g0xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_flash.c 0x00000000   Number         0  stm32g0xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_flash_ex.c 0x00000000   Number         0  stm32g0xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_gpio.c 0x00000000   Number         0  stm32g0xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_pwr.c 0x00000000   Number         0  stm32g0xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g0xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_rcc.c 0x00000000   Number         0  stm32g0xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g0xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_tim.c 0x00000000   Number         0  stm32g0xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_tim_ex.c 0x00000000   Number         0  stm32g0xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_uart.c 0x00000000   Number         0  stm32g0xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32G0xx_HAL_Driver\Src\stm32g0xx_hal_uart_ex.c 0x00000000   Number         0  stm32g0xx_hal_uart_ex.o ABSOLUTE
    ..\Src\adc.c                             0x00000000   Number         0  adc.o ABSOLUTE
    ..\Src\app_system.c                      0x00000000   Number         0  app_system.o ABSOLUTE
    ..\Src\bsp_P9813.c                       0x00000000   Number         0  bsp_p9813.o ABSOLUTE
    ..\Src\bsp_beep.c                        0x00000000   Number         0  bsp_beep.o ABSOLUTE
    ..\Src\bsp_esp8266.c                     0x00000000   Number         0  bsp_esp8266.o ABSOLUTE
    ..\Src\bsp_lcd.c                         0x00000000   Number         0  bsp_lcd.o ABSOLUTE
    ..\Src\bsp_softwareTimer.c               0x00000000   Number         0  bsp_softwaretimer.o ABSOLUTE
    ..\Src\bsp_ultrasonic.c                  0x00000000   Number         0  bsp_ultrasonic.o ABSOLUTE
    ..\Src\dht11.c                           0x00000000   Number         0  dht11.o ABSOLUTE
    ..\Src\dma.c                             0x00000000   Number         0  dma.o ABSOLUTE
    ..\Src\esp8266.c                         0x00000000   Number         0  esp8266.o ABSOLUTE
    ..\Src\fs_crc.c                          0x00000000   Number         0  fs_crc.o ABSOLUTE
    ..\Src\fs_protocol.c                     0x00000000   Number         0  fs_protocol.o ABSOLUTE
    ..\Src\gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Src\key.c                             0x00000000   Number         0  key.o ABSOLUTE
    ..\Src\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\Src\stm32g0xx_hal_msp.c               0x00000000   Number         0  stm32g0xx_hal_msp.o ABSOLUTE
    ..\Src\stm32g0xx_it.c                    0x00000000   Number         0  stm32g0xx_it.o ABSOLUTE
    ..\Src\system_stm32g0xx.c                0x00000000   Number         0  system_stm32g0xx.o ABSOLUTE
    ..\Src\tim.c                             0x00000000   Number         0  tim.o ABSOLUTE
    ..\Src\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\\Src\\app_system.c                    0x00000000   Number         0  app_system.o ABSOLUTE
    ..\\Src\\bsp_P9813.c                     0x00000000   Number         0  bsp_p9813.o ABSOLUTE
    ..\\Src\\bsp_beep.c                      0x00000000   Number         0  bsp_beep.o ABSOLUTE
    ..\\Src\\bsp_esp8266.c                   0x00000000   Number         0  bsp_esp8266.o ABSOLUTE
    ..\\Src\\bsp_lcd.c                       0x00000000   Number         0  bsp_lcd.o ABSOLUTE
    ..\\Src\\bsp_softwareTimer.c             0x00000000   Number         0  bsp_softwaretimer.o ABSOLUTE
    ..\\Src\\bsp_ultrasonic.c                0x00000000   Number         0  bsp_ultrasonic.o ABSOLUTE
    ..\\Src\\dht11.c                         0x00000000   Number         0  dht11.o ABSOLUTE
    ..\\Src\\esp8266.c                       0x00000000   Number         0  esp8266.o ABSOLUTE
    ..\\Src\\fs_protocol.c                   0x00000000   Number         0  fs_protocol.o ABSOLUTE
    ..\\Src\\key.c                           0x00000000   Number         0  key.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32g030xx.s                    0x00000000   Number         0  startup_stm32g030xx.o ABSOLUTE
    RESET                                    0x08000000   Section      192  startup_stm32g030xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080000d4   Section       28  startup_stm32g030xx.o(.text)
    .text                                    0x080000f0   Section        0  uidiv.o(.text)
    .text                                    0x0800011c   Section        0  idiv.o(.text)
    .text                                    0x08000144   Section        0  memcpya.o(.text)
    .text                                    0x08000168   Section        0  memseta.o(.text)
    .text                                    0x0800018c   Section        0  strstr.o(.text)
    .text                                    0x080001b4   Section        0  strlen.o(.text)
    .text                                    0x080001c4   Section        0  strtok.o(.text)
    .text                                    0x0800020c   Section        0  __0sscanf.o(.text)
    .text                                    0x08000248   Section        0  _scanf_str.o(.text)
    .text                                    0x08000330   Section        0  ddiv.o(.text)
    .text                                    0x08000420   Section        0  dfltui.o(.text)
    .text                                    0x0800043c   Section        0  dfixui.o(.text)
    .text                                    0x08000478   Section       40  cdcmple.o(.text)
    .text                                    0x080004a0   Section        0  uldiv.o(.text)
    .text                                    0x08000500   Section        0  llushr.o(.text)
    .text                                    0x08000524   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000525   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000550   Section        0  _sgetc.o(.text)
    .text                                    0x08000594   Section        0  depilogue.o(.text)
    .text                                    0x08000594   Section        0  iusefp.o(.text)
    .text                                    0x08000654   Section       36  init.o(.text)
    .text                                    0x08000678   Section        0  llshl.o(.text)
    .text                                    0x08000698   Section        0  isspace_c.o(.text)
    .text                                    0x080006a8   Section        0  _scanf.o(.text)
    .text                                    0x080009e0   Section        0  ctype_c.o(.text)
    .text                                    0x08000a04   Section        0  __dczerorl2.o(.text)
    i.ADC_DMAConvCplt                        0x08000a5a   Section        0  stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x08000a5b   Thumb Code   144  stm32g0xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08000aea   Section        0  stm32g0xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x08000aeb   Thumb Code    30  stm32g0xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08000b08   Section        0  stm32g0xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x08000b09   Thumb Code    14  stm32g0xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Enable                             0x08000b18   Section        0  stm32g0xx_hal_adc.o(i.ADC_Enable)
    i.ATRecv                                 0x08000ba4   Section        0  bsp_esp8266.o(i.ATRecv)
    ATRecv                                   0x08000ba5   Thumb Code   304  bsp_esp8266.o(i.ATRecv)
    i.ATSend                                 0x08000db8   Section        0  bsp_esp8266.o(i.ATSend)
    ATSend                                   0x08000db9   Thumb Code   220  bsp_esp8266.o(i.ATSend)
    i.Blink_Leds                             0x08000ee0   Section        0  main.o(i.Blink_Leds)
    i.Color_Data                             0x08000f0a   Section        0  bsp_p9813.o(i.Color_Data)
    i.DHT11_Check                            0x08000f40   Section        0  dht11.o(i.DHT11_Check)
    i.DHT11_IO_IN                            0x08000fa0   Section        0  dht11.o(i.DHT11_IO_IN)
    DHT11_IO_IN                              0x08000fa1   Thumb Code    38  dht11.o(i.DHT11_IO_IN)
    i.DHT11_IO_OUT                           0x08000fcc   Section        0  dht11.o(i.DHT11_IO_OUT)
    DHT11_IO_OUT                             0x08000fcd   Thumb Code    40  dht11.o(i.DHT11_IO_OUT)
    i.DHT11_Read_Bit                         0x08000ff8   Section        0  dht11.o(i.DHT11_Read_Bit)
    i.DHT11_Read_Byte                        0x0800105c   Section        0  dht11.o(i.DHT11_Read_Byte)
    i.DHT11_Read_Data                        0x0800107c   Section        0  dht11.o(i.DHT11_Read_Data)
    i.DHT11_Rst                              0x080010f4   Section        0  dht11.o(i.DHT11_Rst)
    i.DMA1_Channel1_IRQHandler               0x08001124   Section        0  stm32g0xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_3_IRQHandler             0x08001134   Section        0  stm32g0xx_it.o(i.DMA1_Channel2_3_IRQHandler)
    i.DMA_CalcDMAMUXChannelBaseAndMask       0x08001144   Section        0  stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    DMA_CalcDMAMUXChannelBaseAndMask         0x08001145   Thumb Code    48  stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    i.DMA_CalcDMAMUXRequestGenBaseAndMask    0x08001178   Section        0  stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    DMA_CalcDMAMUXRequestGenBaseAndMask      0x08001179   Thumb Code    32  stm32g0xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    i.DMA_SetConfig                          0x0800119c   Section        0  stm32g0xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x0800119d   Thumb Code    70  stm32g0xx_hal_dma.o(i.DMA_SetConfig)
    i.DisplayDeviceData                      0x080011e8   Section        0  app_system.o(i.DisplayDeviceData)
    i.ESP8266_Enable_MultipleId              0x080015bc   Section        0  esp8266.o(i.ESP8266_Enable_MultipleId)
    i.ESP8266_JoinAP                         0x08001608   Section        0  esp8266.o(i.ESP8266_JoinAP)
    i.ESP8266_Link_Server                    0x08001670   Section        0  esp8266.o(i.ESP8266_Link_Server)
    i.ESP8266_Net_Mode_Choose                0x0800175c   Section        0  esp8266.o(i.ESP8266_Net_Mode_Choose)
    i.ESP8266_RESTORE                        0x080017e0   Section        0  esp8266.o(i.ESP8266_RESTORE)
    i.ESP8266_Read_IP                        0x0800180c   Section        0  esp8266.o(i.ESP8266_Read_IP)
    i.ESP8266_UnvarnishMode                  0x080018dc   Section        0  esp8266.o(i.ESP8266_UnvarnishMode)
    i.ESP8266_UnvarnishSend                  0x08001914   Section        0  esp8266.o(i.ESP8266_UnvarnishSend)
    i.ESP_TASK                               0x0800195c   Section        0  bsp_esp8266.o(i.ESP_TASK)
    i.Error_Handler                          0x080019c8   Section        0  main.o(i.Error_Handler)
    i.ExecuteCommand                         0x080019cc   Section        0  app_system.o(i.ExecuteCommand)
    i.FS_DHT11_Init                          0x08001abc   Section        0  dht11.o(i.FS_DHT11_Init)
    i.Gui_DrawFont_GBK16                     0x08001afc   Section        0  bsp_lcd.o(i.Gui_DrawFont_GBK16)
    i.Gui_DrawPoint                          0x08001bb0   Section        0  bsp_lcd.o(i.Gui_DrawPoint)
    i.HAL_ADC_AnalogWDGConfig                0x08001bd0   Section        0  stm32g0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig)
    i.HAL_ADC_ConfigChannel                  0x0800205c   Section        0  stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x080023b8   Section        0  stm32g0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x080023ba   Section        0  stm32g0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x080023bc   Section        0  stm32g0xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x080023c0   Section        0  stm32g0xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08002678   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08002734   Section        0  stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_DMA_Abort                          0x08002800   Section        0  stm32g0xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08002898   Section        0  stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_GetError                       0x0800294c   Section        0  stm32g0xx_hal_dma.o(i.HAL_DMA_GetError)
    i.HAL_DMA_IRQHandler                     0x08002954   Section        0  stm32g0xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002a74   Section        0  stm32g0xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002b30   Section        0  stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002c04   Section        0  stm32g0xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08002c30   Section        0  stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08002e00   Section        0  stm32g0xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08002e12   Section        0  stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002e20   Section        0  stm32g0xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002e2c   Section        0  stm32g0xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002e44   Section        0  stm32g0xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002e70   Section        0  stm32g0xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002ed0   Section        0  stm32g0xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002f14   Section        0  stm32g0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002f30   Section        0  stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_PWREx_ControlVoltageScaling        0x08002f44   Section        0  stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_RCCEx_PeriphCLKConfig              0x08002f9c   Section        0  stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08003184   Section        0  stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08003358   Section        0  stm32g0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08003364   Section        0  stm32g0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800338c   Section        0  stm32g0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003470   Section        0  stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08003a24   Section        0  stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_Break2Callback               0x08003a5c   Section        0  stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    i.HAL_TIMEx_BreakCallback                0x08003a5e   Section        0  stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08003a60   Section        0  stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIM_IC_CaptureCallback             0x08003a62   Section        0  stm32g0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08003a64   Section        0  stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08003c5c   Section        0  stm32g0xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08003c5e   Section        0  stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08003c60   Section        0  app_system.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08003c78   Section        0  stm32g0xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_DisableFifoMode             0x08003c7a   Section        0  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_RxFifoFullCallback          0x08003cce   Section        0  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    i.HAL_UARTEx_SetRxFifoThreshold          0x08003cd0   Section        0  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x08003d30   Section        0  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UARTEx_TxFifoEmptyCallback         0x08003d8e   Section        0  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    i.HAL_UARTEx_WakeupCallback              0x08003d90   Section        0  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_DMAStop                       0x08003d92   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08003e2a   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08003e2c   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004068   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080040ec   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_DMA                   0x0800422c   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    i.HAL_UART_RxCpltCallback                0x0800431c   Section        0  stm32g0xx_it.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08004388   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x0800438a   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004492   Section        0  stm32g0xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004494   Section        0  stm32g0xx_it.o(i.HardFault_Handler)
    i.KeyInit                                0x080044bc   Section        0  key.o(i.KeyInit)
    i.LCD_WriteData_16Bit                    0x080044dc   Section        0  bsp_lcd.o(i.LCD_WriteData_16Bit)
    i.LL_ADC_Disable                         0x0800450c   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_Disable)
    LL_ADC_Disable                           0x0800450d   Thumb Code    12  stm32g0xx_hal_adc.o(i.LL_ADC_Disable)
    i.LL_ADC_Enable                          0x0800451c   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_Enable)
    LL_ADC_Enable                            0x0800451d   Thumb Code    12  stm32g0xx_hal_adc.o(i.LL_ADC_Enable)
    i.LL_ADC_GetCommonPathInternalCh         0x0800452c   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_GetCommonPathInternalCh)
    LL_ADC_GetCommonPathInternalCh           0x0800452d   Thumb Code    12  stm32g0xx_hal_adc.o(i.LL_ADC_GetCommonPathInternalCh)
    i.LL_ADC_IsEnabled                       0x08004538   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_IsEnabled)
    LL_ADC_IsEnabled                         0x08004539   Thumb Code    16  stm32g0xx_hal_adc.o(i.LL_ADC_IsEnabled)
    i.LL_ADC_IsInternalRegulatorEnabled      0x08004548   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    LL_ADC_IsInternalRegulatorEnabled        0x08004549   Thumb Code    14  stm32g0xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    i.LL_ADC_REG_IsConversionOngoing         0x08004556   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08004557   Thumb Code    12  stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsTriggerSourceSWStart      0x08004562   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    LL_ADC_REG_IsTriggerSourceSWStart        0x08004563   Thumb Code    22  stm32g0xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    i.LL_ADC_REG_StartConversion             0x08004578   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    LL_ADC_REG_StartConversion               0x08004579   Thumb Code    12  stm32g0xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    i.LL_ADC_SetAnalogWDMonitChannels        0x08004588   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels)
    LL_ADC_SetAnalogWDMonitChannels          0x08004589   Thumb Code    50  stm32g0xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels)
    i.LL_ADC_SetCommonPathInternalCh         0x080045c4   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    LL_ADC_SetCommonPathInternalCh           0x080045c5   Thumb Code    14  stm32g0xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    i.LL_ADC_SetSamplingTimeCommonChannels   0x080045d2   Section        0  stm32g0xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonChannels)
    LL_ADC_SetSamplingTimeCommonChannels     0x080045d3   Thumb Code    30  stm32g0xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonChannels)
    i.Lcd_Clear                              0x080045f0   Section        0  bsp_lcd.o(i.Lcd_Clear)
    i.Lcd_ClearLine                          0x08004622   Section        0  bsp_lcd.o(i.Lcd_ClearLine)
    i.Lcd_Init                               0x08004668   Section        0  bsp_lcd.o(i.Lcd_Init)
    i.Lcd_SetRegion                          0x0800469c   Section        0  bsp_lcd.o(i.Lcd_SetRegion)
    i.Lcd_WriteData                          0x080046f4   Section        0  bsp_lcd.o(i.Lcd_WriteData)
    i.Lcd_WriteIndex                         0x08004720   Section        0  bsp_lcd.o(i.Lcd_WriteIndex)
    i.MX_ADC1_Init                           0x0800474c   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DMA_Init                            0x08004850   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08004894   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART1_UART_Init                    0x0800499c   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004a10   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.NMI_Handler                            0x08004a54   Section        0  stm32g0xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08004a56   Section        0  stm32g0xx_it.o(i.PendSV_Handler)
    i.RGB_Send_Data                          0x08004a58   Section        0  bsp_p9813.o(i.RGB_Send_Data)
    i.Rec_WaitAT                             0x08004ab0   Section        0  bsp_esp8266.o(i.Rec_WaitAT)
    Rec_WaitAT                               0x08004ab1   Thumb Code   528  bsp_esp8266.o(i.Rec_WaitAT)
    i.SEND_TASK                              0x08004dd4   Section        0  bsp_esp8266.o(i.SEND_TASK)
    i.SPI_WriteData                          0x0800517c   Section        0  bsp_lcd.o(i.SPI_WriteData)
    i.SVC_Handler                            0x080051b0   Section        0  stm32g0xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080051b2   Section        0  stm32g0xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080051bc   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005250   Section        0  system_stm32g0xx.o(i.SystemInit)
    i.TIM1_BRK_UP_TRG_COM_IRQHandler         0x08005260   Section        0  stm32g0xx_it.o(i.TIM1_BRK_UP_TRG_COM_IRQHandler)
    i.UARTEx_SetNbDataToProcess              0x08005270   Section        0  stm32g0xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x08005271   Thumb Code   114  stm32g0xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x080052f4   Section        0  stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x080053e4   Section        0  stm32g0xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x08005458   Section        0  stm32g0xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005459   Thumb Code    24  stm32g0xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08005470   Section        0  stm32g0xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08005471   Thumb Code    90  stm32g0xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x080054ca   Section        0  stm32g0xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x080054cb   Thumb Code    76  stm32g0xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08005516   Section        0  stm32g0xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08005517   Thumb Code    14  stm32g0xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08005524   Section        0  stm32g0xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005525   Thumb Code    38  stm32g0xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08005550   Section        0  stm32g0xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08005551   Thumb Code    34  stm32g0xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08005572   Section        0  stm32g0xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08005573   Thumb Code    34  stm32g0xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_SetConfig                         0x08005594   Section        0  stm32g0xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08005be8   Section        0  stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART2_IRQHandler                      0x08005c58   Section        0  stm32g0xx_it.o(i.USART2_IRQHandler)
    i.__0printf$8                            0x08005cc8   Section        0  printf8.o(i.__0printf$8)
    i.__0sprintf$8                           0x08005ce8   Section        0  printf8.o(i.__0sprintf$8)
    i.__ARM_clz                              0x08005d10   Section        0  depilogue.o(i.__ARM_clz)
    i.__ARM_common_switch8                   0x08005d3e   Section        0  stm32g0xx_hal_dma.o(i.__ARM_common_switch8)
    i.__NVIC_SetPriority                     0x08005d5c   Section        0  stm32g0xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08005d5d   Thumb Code   110  stm32g0xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08005dd4   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08005de2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08005de4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08005df4   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x08005df5   Thumb Code  1020  printf8.o(i._printf_core)
    i._printf_post_padding                   0x0800621c   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x0800621d   Thumb Code    32  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x0800623c   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x0800623d   Thumb Code    44  printf8.o(i._printf_pre_padding)
    i._sputc                                 0x08006268   Section        0  printf8.o(i._sputc)
    _sputc                                   0x08006269   Thumb Code    10  printf8.o(i._sputc)
    i.analysis_Packet                        0x08006274   Section        0  fs_protocol.o(i.analysis_Packet)
    i.cmdAT                                  0x080063f4   Section        0  esp8266.o(i.cmdAT)
    i.compareTime                            0x0800654c   Section        0  bsp_softwaretimer.o(i.compareTime)
    i.crc8                                   0x08006566   Section        0  fs_crc.o(i.crc8)
    i.delay_init                             0x080065a4   Section        0  dht11.o(i.delay_init)
    i.delay_us                               0x080065b0   Section        0  dht11.o(i.delay_us)
    i.fputc                                  0x080065f8   Section        0  main.o(i.fputc)
    i.get_SensorData                         0x08006610   Section        0  app_system.o(i.get_SensorData)
    i.get_ultrasonic_val                     0x08006734   Section        0  bsp_ultrasonic.o(i.get_ultrasonic_val)
    i.initEsp8266                            0x080067e4   Section        0  esp8266.o(i.initEsp8266)
    i.main                                   0x08006afc   Section        0  main.o(i.main)
    i.packet_data                            0x08006bec   Section        0  fs_protocol.o(i.packet_data)
    i.reset_rxbuffClear                      0x08006c5c   Section        0  esp8266.o(i.reset_rxbuffClear)
    i.rgb_setValue                           0x08006c94   Section        0  bsp_p9813.o(i.rgb_setValue)
    i.setTime                                0x08006cbc   Section        0  bsp_softwaretimer.o(i.setTime)
    i.set_Beep_Status                        0x08006ccc   Section        0  bsp_beep.o(i.set_Beep_Status)
    i.testAT                                 0x08006cf0   Section        0  esp8266.o(i.testAT)
    .constdata                               0x08006ddc   Section      120  esp8266.o(.constdata)
    .constdata                               0x08006e54   Section       96  system_stm32g0xx.o(.constdata)
    .constdata                               0x08006eb4   Section     6196  bsp_lcd.o(.constdata)
    .constdata                               0x080086e8   Section       64  ctype_c.o(.constdata)
    .conststring                             0x08008728   Section      211  bsp_esp8266.o(.conststring)
    .data                                    0x20000000   Section        9  dht11.o(.data)
    fac_us                                   0x20000000   Data           4  dht11.o(.data)
    .data                                    0x2000000c   Section       12  stm32g0xx_hal.o(.data)
    .data                                    0x20000018   Section        4  system_stm32g0xx.o(.data)
    .data                                    0x20000020   Section      324  bsp_esp8266.o(.data)
    timeESP                                  0x20000020   Data           8  bsp_esp8266.o(.data)
    timeSendData                             0x20000028   Data           8  bsp_esp8266.o(.data)
    ATNextCmdNum                             0x20000031   Data           1  bsp_esp8266.o(.data)
    ATCurrentCmdNum                          0x20000032   Data           1  bsp_esp8266.o(.data)
    ATRecCmdNum                              0x20000033   Data           1  bsp_esp8266.o(.data)
    CurrentRty                               0x20000034   Data           1  bsp_esp8266.o(.data)
    LOCAL_WIFI_IP                            0x20000038   Data           4  bsp_esp8266.o(.data)
    SendLenth                                0x20000160   Data           4  bsp_esp8266.o(.data)
    .data                                    0x20000164   Section       16  app_system.o(.data)
    .data                                    0x20000174   Section        4  strtok.o(.data)
    state                                    0x20000174   Data           4  strtok.o(.data)
    .data                                    0x20000178   Section        4  stdout.o(.data)
    .bss                                     0x2000017c   Section      208  adc.o(.bss)
    .bss                                     0x2000024c   Section      192  tim.o(.bss)
    .bss                                     0x2000030c   Section     1920  usart.o(.bss)
    .bss                                     0x20000a8c   Section       14  esp8266.o(.bss)
    .bss                                     0x20000a9a   Section       50  bsp_esp8266.o(.bss)
    SendBuff                                 0x20000a9a   Data          50  bsp_esp8266.o(.bss)
    .bss                                     0x20000acc   Section       16  app_system.o(.bss)
    STACK                                    0x20000ae0   Section     2048  startup_stm32g030xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_int                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    __Vectors_Size                           0x000000c0   Number         0  startup_stm32g030xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g030xx.o(RESET)
    __Vectors_End                            0x080000c0   Data           0  startup_stm32g030xx.o(RESET)
    __main                                   0x080000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080000d5   Thumb Code     8  startup_stm32g030xx.o(.text)
    ADC1_IRQHandler                          0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    DMA1_Ch4_5_DMAMUX1_OVR_IRQHandler        0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    EXTI0_1_IRQHandler                       0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    EXTI2_3_IRQHandler                       0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    EXTI4_15_IRQHandler                      0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    FLASH_IRQHandler                         0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    I2C1_IRQHandler                          0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    I2C2_IRQHandler                          0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    RCC_IRQHandler                           0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    RTC_TAMP_IRQHandler                      0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    SPI1_IRQHandler                          0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    SPI2_IRQHandler                          0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    TIM14_IRQHandler                         0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    TIM16_IRQHandler                         0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    TIM17_IRQHandler                         0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    TIM1_CC_IRQHandler                       0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    TIM3_IRQHandler                          0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    USART1_IRQHandler                        0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    WWDG_IRQHandler                          0x080000e7   Thumb Code     0  startup_stm32g030xx.o(.text)
    __aeabi_uidiv                            0x080000f1   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080000f1   Thumb Code    44  uidiv.o(.text)
    __aeabi_idiv                             0x0800011d   Thumb Code     0  idiv.o(.text)
    __aeabi_idivmod                          0x0800011d   Thumb Code    40  idiv.o(.text)
    __aeabi_memcpy                           0x08000145   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000145   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000145   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000169   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000169   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000169   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000177   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000177   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000177   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800017b   Thumb Code    18  memseta.o(.text)
    strstr                                   0x0800018d   Thumb Code    40  strstr.o(.text)
    strlen                                   0x080001b5   Thumb Code    14  strlen.o(.text)
    strtok                                   0x080001c5   Thumb Code    68  strtok.o(.text)
    __0sscanf                                0x0800020d   Thumb Code    50  __0sscanf.o(.text)
    _scanf_string                            0x08000249   Thumb Code   232  _scanf_str.o(.text)
    __aeabi_ddiv                             0x08000331   Thumb Code   234  ddiv.o(.text)
    __aeabi_ui2d                             0x08000421   Thumb Code    24  dfltui.o(.text)
    __aeabi_d2uiz                            0x0800043d   Thumb Code    50  dfixui.o(.text)
    __aeabi_cdcmpeq                          0x08000479   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000479   Thumb Code    38  cdcmple.o(.text)
    __aeabi_uldivmod                         0x080004a1   Thumb Code    96  uldiv.o(.text)
    __aeabi_llsr                             0x08000501   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x08000501   Thumb Code     0  llushr.o(.text)
    __vfscanf_char                           0x08000531   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000551   Thumb Code    32  _sgetc.o(.text)
    _sbackspace                              0x08000571   Thumb Code    36  _sgetc.o(.text)
    __I$use$fp                               0x08000595   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000595   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x080005af   Thumb Code   164  depilogue.o(.text)
    __scatterload                            0x08000655   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000655   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000679   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x08000679   Thumb Code     0  llshl.o(.text)
    isspace                                  0x08000699   Thumb Code    14  isspace_c.o(.text)
    __vfscanf                                0x080006a9   Thumb Code   812  _scanf.o(.text)
    __ctype_lookup                           0x080009e1   Thumb Code    32  ctype_c.o(.text)
    __decompress                             0x08000a05   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000a05   Thumb Code    86  __dczerorl2.o(.text)
    ADC_Enable                               0x08000b19   Thumb Code   134  stm32g0xx_hal_adc.o(i.ADC_Enable)
    Blink_Leds                               0x08000ee1   Thumb Code    42  main.o(i.Blink_Leds)
    Color_Data                               0x08000f0b   Thumb Code    52  bsp_p9813.o(i.Color_Data)
    DHT11_Check                              0x08000f41   Thumb Code    90  dht11.o(i.DHT11_Check)
    DHT11_Read_Bit                           0x08000ff9   Thumb Code    94  dht11.o(i.DHT11_Read_Bit)
    DHT11_Read_Byte                          0x0800105d   Thumb Code    30  dht11.o(i.DHT11_Read_Byte)
    DHT11_Read_Data                          0x0800107d   Thumb Code   104  dht11.o(i.DHT11_Read_Data)
    DHT11_Rst                                0x080010f5   Thumb Code    42  dht11.o(i.DHT11_Rst)
    DMA1_Channel1_IRQHandler                 0x08001125   Thumb Code    10  stm32g0xx_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_3_IRQHandler               0x08001135   Thumb Code    10  stm32g0xx_it.o(i.DMA1_Channel2_3_IRQHandler)
    DisplayDeviceData                        0x080011e9   Thumb Code   824  app_system.o(i.DisplayDeviceData)
    ESP8266_Enable_MultipleId                0x080015bd   Thumb Code    50  esp8266.o(i.ESP8266_Enable_MultipleId)
    ESP8266_JoinAP                           0x08001609   Thumb Code    44  esp8266.o(i.ESP8266_JoinAP)
    ESP8266_Link_Server                      0x08001671   Thumb Code   122  esp8266.o(i.ESP8266_Link_Server)
    ESP8266_Net_Mode_Choose                  0x0800175d   Thumb Code    88  esp8266.o(i.ESP8266_Net_Mode_Choose)
    ESP8266_RESTORE                          0x080017e1   Thumb Code    24  esp8266.o(i.ESP8266_RESTORE)
    ESP8266_Read_IP                          0x0800180d   Thumb Code   126  esp8266.o(i.ESP8266_Read_IP)
    ESP8266_UnvarnishMode                    0x080018dd   Thumb Code    32  esp8266.o(i.ESP8266_UnvarnishMode)
    ESP8266_UnvarnishSend                    0x08001915   Thumb Code    24  esp8266.o(i.ESP8266_UnvarnishSend)
    ESP_TASK                                 0x0800195d   Thumb Code    88  bsp_esp8266.o(i.ESP_TASK)
    Error_Handler                            0x080019c9   Thumb Code     2  main.o(i.Error_Handler)
    ExecuteCommand                           0x080019cd   Thumb Code   168  app_system.o(i.ExecuteCommand)
    FS_DHT11_Init                            0x08001abd   Thumb Code    58  dht11.o(i.FS_DHT11_Init)
    Gui_DrawFont_GBK16                       0x08001afd   Thumb Code   176  bsp_lcd.o(i.Gui_DrawFont_GBK16)
    Gui_DrawPoint                            0x08001bb1   Thumb Code    32  bsp_lcd.o(i.Gui_DrawPoint)
    HAL_ADC_AnalogWDGConfig                  0x08001bd1   Thumb Code  1158  stm32g0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig)
    HAL_ADC_ConfigChannel                    0x0800205d   Thumb Code   832  stm32g0xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x080023b9   Thumb Code     2  stm32g0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x080023bb   Thumb Code     2  stm32g0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x080023bd   Thumb Code     2  stm32g0xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x080023c1   Thumb Code   664  stm32g0xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08002679   Thumb Code   172  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08002735   Thumb Code   186  stm32g0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_DMA_Abort                            0x08002801   Thumb Code   146  stm32g0xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002899   Thumb Code   174  stm32g0xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_GetError                         0x0800294d   Thumb Code     6  stm32g0xx_hal_dma.o(i.HAL_DMA_GetError)
    HAL_DMA_IRQHandler                       0x08002955   Thumb Code   282  stm32g0xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002a75   Thumb Code   178  stm32g0xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002b31   Thumb Code   210  stm32g0xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002c05   Thumb Code    38  stm32g0xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002c31   Thumb Code   448  stm32g0xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08002e01   Thumb Code    18  stm32g0xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08002e13   Thumb Code    12  stm32g0xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002e21   Thumb Code     6  stm32g0xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002e2d   Thumb Code    16  stm32g0xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002e45   Thumb Code    40  stm32g0xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002e71   Thumb Code    82  stm32g0xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002ed1   Thumb Code    64  stm32g0xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002f15   Thumb Code    22  stm32g0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002f31   Thumb Code    18  stm32g0xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_PWREx_ControlVoltageScaling          0x08002f45   Thumb Code    76  stm32g0xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_RCCEx_PeriphCLKConfig                0x08002f9d   Thumb Code   474  stm32g0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08003185   Thumb Code   442  stm32g0xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08003359   Thumb Code     6  stm32g0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003365   Thumb Code    32  stm32g0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetSysClockFreq                  0x0800338d   Thumb Code   216  stm32g0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003471   Thumb Code  1442  stm32g0xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08003a25   Thumb Code    46  stm32g0xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_Break2Callback                 0x08003a5d   Thumb Code     2  stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    HAL_TIMEx_BreakCallback                  0x08003a5f   Thumb Code     2  stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08003a61   Thumb Code     2  stm32g0xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIM_IC_CaptureCallback               0x08003a63   Thumb Code     2  stm32g0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08003a65   Thumb Code   498  stm32g0xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08003c5d   Thumb Code     2  stm32g0xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003c5f   Thumb Code     2  stm32g0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08003c61   Thumb Code    16  app_system.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08003c79   Thumb Code     2  stm32g0xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_DisableFifoMode               0x08003c7b   Thumb Code    84  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_RxFifoFullCallback            0x08003ccf   Thumb Code     2  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x08003cd1   Thumb Code    96  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x08003d31   Thumb Code    94  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x08003d8f   Thumb Code     2  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x08003d91   Thumb Code     2  stm32g0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_DMAStop                         0x08003d93   Thumb Code   152  stm32g0xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08003e2b   Thumb Code     2  stm32g0xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003e2d   Thumb Code   562  stm32g0xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004069   Thumb Code   130  stm32g0xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080040ed   Thumb Code   300  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_DMA                     0x0800422d   Thumb Code   228  stm32g0xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    HAL_UART_RxCpltCallback                  0x0800431d   Thumb Code    98  stm32g0xx_it.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08004389   Thumb Code     2  stm32g0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x0800438b   Thumb Code   264  stm32g0xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004493   Thumb Code     2  stm32g0xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004495   Thumb Code    34  stm32g0xx_it.o(i.HardFault_Handler)
    KeyInit                                  0x080044bd   Thumb Code    22  key.o(i.KeyInit)
    LCD_WriteData_16Bit                      0x080044dd   Thumb Code    44  bsp_lcd.o(i.LCD_WriteData_16Bit)
    Lcd_Clear                                0x080045f1   Thumb Code    50  bsp_lcd.o(i.Lcd_Clear)
    Lcd_ClearLine                            0x08004623   Thumb Code    70  bsp_lcd.o(i.Lcd_ClearLine)
    Lcd_Init                                 0x08004669   Thumb Code    52  bsp_lcd.o(i.Lcd_Init)
    Lcd_SetRegion                            0x0800469d   Thumb Code    86  bsp_lcd.o(i.Lcd_SetRegion)
    Lcd_WriteData                            0x080046f5   Thumb Code    38  bsp_lcd.o(i.Lcd_WriteData)
    Lcd_WriteIndex                           0x08004721   Thumb Code    38  bsp_lcd.o(i.Lcd_WriteIndex)
    MX_ADC1_Init                             0x0800474d   Thumb Code   238  adc.o(i.MX_ADC1_Init)
    MX_DMA_Init                              0x08004851   Thumb Code    64  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08004895   Thumb Code   256  gpio.o(i.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x0800499d   Thumb Code   106  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004a11   Thumb Code    60  usart.o(i.MX_USART2_UART_Init)
    NMI_Handler                              0x08004a55   Thumb Code     2  stm32g0xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08004a57   Thumb Code     2  stm32g0xx_it.o(i.PendSV_Handler)
    RGB_Send_Data                            0x08004a59   Thumb Code    82  bsp_p9813.o(i.RGB_Send_Data)
    SEND_TASK                                0x08004dd5   Thumb Code   632  bsp_esp8266.o(i.SEND_TASK)
    SPI_WriteData                            0x0800517d   Thumb Code    48  bsp_lcd.o(i.SPI_WriteData)
    SVC_Handler                              0x080051b1   Thumb Code     2  stm32g0xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080051b3   Thumb Code     8  stm32g0xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080051bd   Thumb Code   142  main.o(i.SystemClock_Config)
    SystemInit                               0x08005251   Thumb Code    10  system_stm32g0xx.o(i.SystemInit)
    TIM1_BRK_UP_TRG_COM_IRQHandler           0x08005261   Thumb Code    10  stm32g0xx_it.o(i.TIM1_BRK_UP_TRG_COM_IRQHandler)
    UART_AdvFeatureConfig                    0x080052f5   Thumb Code   240  stm32g0xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x080053e5   Thumb Code   112  stm32g0xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08005595   Thumb Code  1612  stm32g0xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x08005be9   Thumb Code   112  stm32g0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART2_IRQHandler                        0x08005c59   Thumb Code    98  stm32g0xx_it.o(i.USART2_IRQHandler)
    __0printf$8                              0x08005cc9   Thumb Code    24  printf8.o(i.__0printf$8)
    __1printf$8                              0x08005cc9   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x08005cc9   Thumb Code     0  printf8.o(i.__0printf$8)
    __0sprintf$8                             0x08005ce9   Thumb Code    36  printf8.o(i.__0sprintf$8)
    __1sprintf$8                             0x08005ce9   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __2sprintf                               0x08005ce9   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __ARM_clz                                0x08005d11   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __ARM_common_switch8                     0x08005d3f   Thumb Code    28  stm32g0xx_hal_dma.o(i.__ARM_common_switch8)
    __scatterload_copy                       0x08005dd5   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08005de3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08005de5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    analysis_Packet                          0x08006275   Thumb Code   322  fs_protocol.o(i.analysis_Packet)
    cmdAT                                    0x080063f5   Thumb Code   314  esp8266.o(i.cmdAT)
    compareTime                              0x0800654d   Thumb Code    26  bsp_softwaretimer.o(i.compareTime)
    crc8                                     0x08006567   Thumb Code    62  fs_crc.o(i.crc8)
    delay_init                               0x080065a5   Thumb Code     6  dht11.o(i.delay_init)
    delay_us                                 0x080065b1   Thumb Code    62  dht11.o(i.delay_us)
    fputc                                    0x080065f9   Thumb Code    20  main.o(i.fputc)
    get_SensorData                           0x08006611   Thumb Code   270  app_system.o(i.get_SensorData)
    get_ultrasonic_val                       0x08006735   Thumb Code   164  bsp_ultrasonic.o(i.get_ultrasonic_val)
    initEsp8266                              0x080067e5   Thumb Code   260  esp8266.o(i.initEsp8266)
    main                                     0x08006afd   Thumb Code   192  main.o(i.main)
    packet_data                              0x08006bed   Thumb Code   106  fs_protocol.o(i.packet_data)
    reset_rxbuffClear                        0x08006c5d   Thumb Code    36  esp8266.o(i.reset_rxbuffClear)
    rgb_setValue                             0x08006c95   Thumb Code    40  bsp_p9813.o(i.rgb_setValue)
    setTime                                  0x08006cbd   Thumb Code    16  bsp_softwaretimer.o(i.setTime)
    set_Beep_Status                          0x08006ccd   Thumb Code    32  bsp_beep.o(i.set_Beep_Status)
    testAT                                   0x08006cf1   Thumb Code   152  esp8266.o(i.testAT)
    AHBPrescTable                            0x08006e54   Data          64  system_stm32g0xx.o(.constdata)
    APBPrescTable                            0x08006e94   Data          32  system_stm32g0xx.o(.constdata)
    asc16                                    0x08006eb4   Data        1521  bsp_lcd.o(.constdata)
    Zk_ASCII8X16                             0x080074a5   Data        4097  bsp_lcd.o(.constdata)
    hz16                                     0x080084a6   Data         578  bsp_lcd.o(.constdata)
    __ctype_categories                       0x080086e8   Data          64  ctype_c.o(.constdata)
    Region$$Table$$Base                      0x080087fc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800881c   Number         0  anon$$obj.o(Region$$Table)
    ucharT_data_H                            0x20000004   Data           1  dht11.o(.data)
    ucharT_data_L                            0x20000005   Data           1  dht11.o(.data)
    ucharRH_data_H                           0x20000006   Data           1  dht11.o(.data)
    ucharRH_data_L                           0x20000007   Data           1  dht11.o(.data)
    ucharcheckdata                           0x20000008   Data           1  dht11.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32g0xx_hal.o(.data)
    uwTickPrio                               0x20000010   Data           4  stm32g0xx_hal.o(.data)
    uwTickFreq                               0x20000014   Data           4  stm32g0xx_hal.o(.data)
    SystemCoreClock                          0x20000018   Data           4  system_stm32g0xx.o(.data)
    ESP_TaskStatus                           0x20000030   Data           1  bsp_esp8266.o(.data)
    ATCmds                                   0x20000040   Data         288  bsp_esp8266.o(.data)
    global_Data                              0x20000164   Data          16  app_system.o(.data)
    __stdout                                 0x20000178   Data           4  stdout.o(.data)
    hadc1                                    0x2000017c   Data         100  adc.o(.bss)
    hdma_adc1                                0x200001e0   Data          92  adc.o(.bss)
    ADC_DMA_Value                            0x2000023c   Data          16  adc.o(.bss)
    htim1                                    0x2000024c   Data          64  tim.o(.bss)
    htim3                                    0x2000028c   Data          64  tim.o(.bss)
    htim17                                   0x200002cc   Data          64  tim.o(.bss)
    Usart1type                               0x2000030c   Data         774  usart.o(.bss)
    Usart2type                               0x20000612   Data         774  usart.o(.bss)
    huart1                                   0x20000918   Data         140  usart.o(.bss)
    huart2                                   0x200009a4   Data         140  usart.o(.bss)
    hdma_usart2_rx                           0x20000a30   Data          92  usart.o(.bss)
    StationIPV4                              0x20000a8c   Data          14  esp8266.o(.bss)
    ADC_DMA_Value_Buff                       0x20000acc   Data          16  app_system.o(.bss)
    __initial_sp                             0x200012e0   Data           0  startup_stm32g030xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000c1

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008998, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x00008874])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000881c, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000c0   Data   RO            3    RESET               startup_stm32g030xx.o
    0x080000c0   0x080000c0   0x00000000   Code   RO         4112  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x080000c0   0x080000c0   0x00000004   Code   RO         4408    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x080000c4   0x080000c4   0x00000004   Code   RO         4411    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x080000c8   0x080000c8   0x00000000   Code   RO         4413    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x080000c8   0x080000c8   0x00000000   Code   RO         4415    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x080000c8   0x080000c8   0x00000008   Code   RO         4416    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x080000d0   0x080000d0   0x00000000   Code   RO         4418    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x080000d0   0x080000d0   0x00000000   Code   RO         4420    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x080000d0   0x080000d0   0x00000004   Code   RO         4409    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x080000d4   0x080000d4   0x0000001c   Code   RO            4    .text               startup_stm32g030xx.o
    0x080000f0   0x080000f0   0x0000002c   Code   RO         4115    .text               mc_p.l(uidiv.o)
    0x0800011c   0x0800011c   0x00000028   Code   RO         4117    .text               mc_p.l(idiv.o)
    0x08000144   0x08000144   0x00000024   Code   RO         4119    .text               mc_p.l(memcpya.o)
    0x08000168   0x08000168   0x00000024   Code   RO         4121    .text               mc_p.l(memseta.o)
    0x0800018c   0x0800018c   0x00000028   Code   RO         4123    .text               mc_p.l(strstr.o)
    0x080001b4   0x080001b4   0x0000000e   Code   RO         4125    .text               mc_p.l(strlen.o)
    0x080001c2   0x080001c2   0x00000002   PAD
    0x080001c4   0x080001c4   0x00000048   Code   RO         4127    .text               mc_p.l(strtok.o)
    0x0800020c   0x0800020c   0x0000003c   Code   RO         4396    .text               mc_p.l(__0sscanf.o)
    0x08000248   0x08000248   0x000000e8   Code   RO         4398    .text               mc_p.l(_scanf_str.o)
    0x08000330   0x08000330   0x000000f0   Code   RO         4400    .text               mf_p.l(ddiv.o)
    0x08000420   0x08000420   0x0000001c   Code   RO         4402    .text               mf_p.l(dfltui.o)
    0x0800043c   0x0800043c   0x0000003c   Code   RO         4404    .text               mf_p.l(dfixui.o)
    0x08000478   0x08000478   0x00000028   Code   RO         4406    .text               mf_p.l(cdcmple.o)
    0x080004a0   0x080004a0   0x00000060   Code   RO         4423    .text               mc_p.l(uldiv.o)
    0x08000500   0x08000500   0x00000022   Code   RO         4425    .text               mc_p.l(llushr.o)
    0x08000522   0x08000522   0x00000002   PAD
    0x08000524   0x08000524   0x0000002c   Code   RO         4427    .text               mc_p.l(scanf_char.o)
    0x08000550   0x08000550   0x00000044   Code   RO         4429    .text               mc_p.l(_sgetc.o)
    0x08000594   0x08000594   0x00000000   Code   RO         4431    .text               mc_p.l(iusefp.o)
    0x08000594   0x08000594   0x000000be   Code   RO         4432    .text               mf_p.l(depilogue.o)
    0x08000652   0x08000652   0x00000002   PAD
    0x08000654   0x08000654   0x00000024   Code   RO         4444    .text               mc_p.l(init.o)
    0x08000678   0x08000678   0x00000020   Code   RO         4446    .text               mc_p.l(llshl.o)
    0x08000698   0x08000698   0x0000000e   Code   RO         4450    .text               mc_p.l(isspace_c.o)
    0x080006a6   0x080006a6   0x00000002   PAD
    0x080006a8   0x080006a8   0x00000338   Code   RO         4452    .text               mc_p.l(_scanf.o)
    0x080009e0   0x080009e0   0x00000024   Code   RO         4454    .text               mc_p.l(ctype_c.o)
    0x08000a04   0x08000a04   0x00000056   Code   RO         4465    .text               mc_p.l(__dczerorl2.o)
    0x08000a5a   0x08000a5a   0x00000090   Code   RO          844    i.ADC_DMAConvCplt   stm32g0xx_hal_adc.o
    0x08000aea   0x08000aea   0x0000001e   Code   RO          845    i.ADC_DMAError      stm32g0xx_hal_adc.o
    0x08000b08   0x08000b08   0x0000000e   Code   RO          846    i.ADC_DMAHalfConvCplt  stm32g0xx_hal_adc.o
    0x08000b16   0x08000b16   0x00000002   PAD
    0x08000b18   0x08000b18   0x0000008c   Code   RO          848    i.ADC_Enable        stm32g0xx_hal_adc.o
    0x08000ba4   0x08000ba4   0x00000214   Code   RO         3765    i.ATRecv            bsp_esp8266.o
    0x08000db8   0x08000db8   0x00000128   Code   RO         3766    i.ATSend            bsp_esp8266.o
    0x08000ee0   0x08000ee0   0x0000002a   Code   RO           12    i.Blink_Leds        main.o
    0x08000f0a   0x08000f0a   0x00000034   Code   RO         3939    i.Color_Data        bsp_p9813.o
    0x08000f3e   0x08000f3e   0x00000002   PAD
    0x08000f40   0x08000f40   0x00000060   Code   RO          691    i.DHT11_Check       dht11.o
    0x08000fa0   0x08000fa0   0x0000002c   Code   RO          692    i.DHT11_IO_IN       dht11.o
    0x08000fcc   0x08000fcc   0x0000002c   Code   RO          693    i.DHT11_IO_OUT      dht11.o
    0x08000ff8   0x08000ff8   0x00000064   Code   RO          694    i.DHT11_Read_Bit    dht11.o
    0x0800105c   0x0800105c   0x0000001e   Code   RO          695    i.DHT11_Read_Byte   dht11.o
    0x0800107a   0x0800107a   0x00000002   PAD
    0x0800107c   0x0800107c   0x00000078   Code   RO          696    i.DHT11_Read_Data   dht11.o
    0x080010f4   0x080010f4   0x00000030   Code   RO          697    i.DHT11_Rst         dht11.o
    0x08001124   0x08001124   0x00000010   Code   RO          437    i.DMA1_Channel1_IRQHandler  stm32g0xx_it.o
    0x08001134   0x08001134   0x00000010   Code   RO          438    i.DMA1_Channel2_3_IRQHandler  stm32g0xx_it.o
    0x08001144   0x08001144   0x00000034   Code   RO         1509    i.DMA_CalcDMAMUXChannelBaseAndMask  stm32g0xx_hal_dma.o
    0x08001178   0x08001178   0x00000024   Code   RO         1510    i.DMA_CalcDMAMUXRequestGenBaseAndMask  stm32g0xx_hal_dma.o
    0x0800119c   0x0800119c   0x0000004c   Code   RO         1511    i.DMA_SetConfig     stm32g0xx_hal_dma.o
    0x080011e8   0x080011e8   0x000003d4   Code   RO         4030    i.DisplayDeviceData  app_system.o
    0x080015bc   0x080015bc   0x0000004c   Code   RO          566    i.ESP8266_Enable_MultipleId  esp8266.o
    0x08001608   0x08001608   0x00000068   Code   RO          567    i.ESP8266_JoinAP    esp8266.o
    0x08001670   0x08001670   0x000000ec   Code   RO          569    i.ESP8266_Link_Server  esp8266.o
    0x0800175c   0x0800175c   0x00000084   Code   RO          570    i.ESP8266_Net_Mode_Choose  esp8266.o
    0x080017e0   0x080017e0   0x0000002c   Code   RO          571    i.ESP8266_RESTORE   esp8266.o
    0x0800180c   0x0800180c   0x000000d0   Code   RO          572    i.ESP8266_Read_IP   esp8266.o
    0x080018dc   0x080018dc   0x00000038   Code   RO          573    i.ESP8266_UnvarnishMode  esp8266.o
    0x08001914   0x08001914   0x00000048   Code   RO          574    i.ESP8266_UnvarnishSend  esp8266.o
    0x0800195c   0x0800195c   0x0000006c   Code   RO         3768    i.ESP_TASK          bsp_esp8266.o
    0x080019c8   0x080019c8   0x00000002   Code   RO           13    i.Error_Handler     main.o
    0x080019ca   0x080019ca   0x00000002   PAD
    0x080019cc   0x080019cc   0x000000f0   Code   RO         4032    i.ExecuteCommand    app_system.o
    0x08001abc   0x08001abc   0x00000040   Code   RO          698    i.FS_DHT11_Init     dht11.o
    0x08001afc   0x08001afc   0x000000b4   Code   RO         3824    i.Gui_DrawFont_GBK16  bsp_lcd.o
    0x08001bb0   0x08001bb0   0x00000020   Code   RO         3825    i.Gui_DrawPoint     bsp_lcd.o
    0x08001bd0   0x08001bd0   0x0000048c   Code   RO          849    i.HAL_ADC_AnalogWDGConfig  stm32g0xx_hal_adc.o
    0x0800205c   0x0800205c   0x0000035c   Code   RO          850    i.HAL_ADC_ConfigChannel  stm32g0xx_hal_adc.o
    0x080023b8   0x080023b8   0x00000002   Code   RO          851    i.HAL_ADC_ConvCpltCallback  stm32g0xx_hal_adc.o
    0x080023ba   0x080023ba   0x00000002   Code   RO          852    i.HAL_ADC_ConvHalfCpltCallback  stm32g0xx_hal_adc.o
    0x080023bc   0x080023bc   0x00000002   Code   RO          854    i.HAL_ADC_ErrorCallback  stm32g0xx_hal_adc.o
    0x080023be   0x080023be   0x00000002   PAD
    0x080023c0   0x080023c0   0x000002b8   Code   RO          859    i.HAL_ADC_Init      stm32g0xx_hal_adc.o
    0x08002678   0x08002678   0x000000bc   Code   RO          268    i.HAL_ADC_MspInit   adc.o
    0x08002734   0x08002734   0x000000cc   Code   RO          866    i.HAL_ADC_Start_DMA  stm32g0xx_hal_adc.o
    0x08002800   0x08002800   0x00000098   Code   RO         1512    i.HAL_DMA_Abort     stm32g0xx_hal_dma.o
    0x08002898   0x08002898   0x000000b4   Code   RO         1513    i.HAL_DMA_Abort_IT  stm32g0xx_hal_dma.o
    0x0800294c   0x0800294c   0x00000006   Code   RO         1515    i.HAL_DMA_GetError  stm32g0xx_hal_dma.o
    0x08002952   0x08002952   0x00000002   PAD
    0x08002954   0x08002954   0x00000120   Code   RO         1517    i.HAL_DMA_IRQHandler  stm32g0xx_hal_dma.o
    0x08002a74   0x08002a74   0x000000bc   Code   RO         1518    i.HAL_DMA_Init      stm32g0xx_hal_dma.o
    0x08002b30   0x08002b30   0x000000d2   Code   RO         1522    i.HAL_DMA_Start_IT  stm32g0xx_hal_dma.o
    0x08002c02   0x08002c02   0x00000002   PAD
    0x08002c04   0x08002c04   0x0000002c   Code   RO         1969    i.HAL_Delay         stm32g0xx_hal.o
    0x08002c30   0x08002c30   0x000001d0   Code   RO          779    i.HAL_GPIO_Init     stm32g0xx_hal_gpio.o
    0x08002e00   0x08002e00   0x00000012   Code   RO          781    i.HAL_GPIO_ReadPin  stm32g0xx_hal_gpio.o
    0x08002e12   0x08002e12   0x0000000c   Code   RO          783    i.HAL_GPIO_WritePin  stm32g0xx_hal_gpio.o
    0x08002e1e   0x08002e1e   0x00000002   PAD
    0x08002e20   0x08002e20   0x0000000c   Code   RO         1973    i.HAL_GetTick       stm32g0xx_hal.o
    0x08002e2c   0x08002e2c   0x00000018   Code   RO         1979    i.HAL_IncTick       stm32g0xx_hal.o
    0x08002e44   0x08002e44   0x0000002c   Code   RO         1980    i.HAL_Init          stm32g0xx_hal.o
    0x08002e70   0x08002e70   0x00000060   Code   RO         1981    i.HAL_InitTick      stm32g0xx_hal.o
    0x08002ed0   0x08002ed0   0x00000044   Code   RO          518    i.HAL_MspInit       stm32g0xx_hal_msp.o
    0x08002f14   0x08002f14   0x0000001c   Code   RO         1857    i.HAL_NVIC_EnableIRQ  stm32g0xx_hal_cortex.o
    0x08002f30   0x08002f30   0x00000012   Code   RO         1861    i.HAL_NVIC_SetPriority  stm32g0xx_hal_cortex.o
    0x08002f42   0x08002f42   0x00000002   PAD
    0x08002f44   0x08002f44   0x00000058   Code   RO         1742    i.HAL_PWREx_ControlVoltageScaling  stm32g0xx_hal_pwr_ex.o
    0x08002f9c   0x08002f9c   0x000001e8   Code   RO         1269    i.HAL_RCCEx_PeriphCLKConfig  stm32g0xx_hal_rcc_ex.o
    0x08003184   0x08003184   0x000001d4   Code   RO         1159    i.HAL_RCC_ClockConfig  stm32g0xx_hal_rcc.o
    0x08003358   0x08003358   0x0000000c   Code   RO         1165    i.HAL_RCC_GetHCLKFreq  stm32g0xx_hal_rcc.o
    0x08003364   0x08003364   0x00000028   Code   RO         1167    i.HAL_RCC_GetPCLK1Freq  stm32g0xx_hal_rcc.o
    0x0800338c   0x0800338c   0x000000e4   Code   RO         1168    i.HAL_RCC_GetSysClockFreq  stm32g0xx_hal_rcc.o
    0x08003470   0x08003470   0x000005b4   Code   RO         1172    i.HAL_RCC_OscConfig  stm32g0xx_hal_rcc.o
    0x08003a24   0x08003a24   0x00000038   Code   RO         1865    i.HAL_SYSTICK_Config  stm32g0xx_hal_cortex.o
    0x08003a5c   0x08003a5c   0x00000002   Code   RO         2920    i.HAL_TIMEx_Break2Callback  stm32g0xx_hal_tim_ex.o
    0x08003a5e   0x08003a5e   0x00000002   Code   RO         2921    i.HAL_TIMEx_BreakCallback  stm32g0xx_hal_tim_ex.o
    0x08003a60   0x08003a60   0x00000002   Code   RO         2922    i.HAL_TIMEx_CommutCallback  stm32g0xx_hal_tim_ex.o
    0x08003a62   0x08003a62   0x00000002   Code   RO         2257    i.HAL_TIM_IC_CaptureCallback  stm32g0xx_hal_tim.o
    0x08003a64   0x08003a64   0x000001f8   Code   RO         2271    i.HAL_TIM_IRQHandler  stm32g0xx_hal_tim.o
    0x08003c5c   0x08003c5c   0x00000002   Code   RO         2274    i.HAL_TIM_OC_DelayElapsedCallback  stm32g0xx_hal_tim.o
    0x08003c5e   0x08003c5e   0x00000002   Code   RO         2301    i.HAL_TIM_PWM_PulseFinishedCallback  stm32g0xx_hal_tim.o
    0x08003c60   0x08003c60   0x00000018   Code   RO         4033    i.HAL_TIM_PeriodElapsedCallback  app_system.o
    0x08003c78   0x08003c78   0x00000002   Code   RO         2314    i.HAL_TIM_TriggerCallback  stm32g0xx_hal_tim.o
    0x08003c7a   0x08003c7a   0x00000054   Code   RO         3582    i.HAL_UARTEx_DisableFifoMode  stm32g0xx_hal_uart_ex.o
    0x08003cce   0x08003cce   0x00000002   Code   RO         3586    i.HAL_UARTEx_RxFifoFullCallback  stm32g0xx_hal_uart_ex.o
    0x08003cd0   0x08003cd0   0x00000060   Code   RO         3587    i.HAL_UARTEx_SetRxFifoThreshold  stm32g0xx_hal_uart_ex.o
    0x08003d30   0x08003d30   0x0000005e   Code   RO         3588    i.HAL_UARTEx_SetTxFifoThreshold  stm32g0xx_hal_uart_ex.o
    0x08003d8e   0x08003d8e   0x00000002   Code   RO         3590    i.HAL_UARTEx_TxFifoEmptyCallback  stm32g0xx_hal_uart_ex.o
    0x08003d90   0x08003d90   0x00000002   Code   RO         3591    i.HAL_UARTEx_WakeupCallback  stm32g0xx_hal_uart_ex.o
    0x08003d92   0x08003d92   0x00000098   Code   RO         3223    i.HAL_UART_DMAStop  stm32g0xx_hal_uart.o
    0x08003e2a   0x08003e2a   0x00000002   Code   RO         3225    i.HAL_UART_ErrorCallback  stm32g0xx_hal_uart.o
    0x08003e2c   0x08003e2c   0x0000023c   Code   RO         3228    i.HAL_UART_IRQHandler  stm32g0xx_hal_uart.o
    0x08004068   0x08004068   0x00000082   Code   RO         3229    i.HAL_UART_Init     stm32g0xx_hal_uart.o
    0x080040ea   0x080040ea   0x00000002   PAD
    0x080040ec   0x080040ec   0x00000140   Code   RO          394    i.HAL_UART_MspInit  usart.o
    0x0800422c   0x0800422c   0x000000f0   Code   RO         3233    i.HAL_UART_Receive_DMA  stm32g0xx_hal_uart.o
    0x0800431c   0x0800431c   0x0000006c   Code   RO          439    i.HAL_UART_RxCpltCallback  stm32g0xx_it.o
    0x08004388   0x08004388   0x00000002   Code   RO         3236    i.HAL_UART_RxHalfCpltCallback  stm32g0xx_hal_uart.o
    0x0800438a   0x0800438a   0x00000108   Code   RO         3237    i.HAL_UART_Transmit  stm32g0xx_hal_uart.o
    0x08004492   0x08004492   0x00000002   Code   RO         3240    i.HAL_UART_TxCpltCallback  stm32g0xx_hal_uart.o
    0x08004494   0x08004494   0x00000028   Code   RO          440    i.HardFault_Handler  stm32g0xx_it.o
    0x080044bc   0x080044bc   0x00000020   Code   RO          539    i.KeyInit           key.o
    0x080044dc   0x080044dc   0x00000030   Code   RO         3826    i.LCD_WriteData_16Bit  bsp_lcd.o
    0x0800450c   0x0800450c   0x00000010   Code   RO          871    i.LL_ADC_Disable    stm32g0xx_hal_adc.o
    0x0800451c   0x0800451c   0x00000010   Code   RO          872    i.LL_ADC_Enable     stm32g0xx_hal_adc.o
    0x0800452c   0x0800452c   0x0000000c   Code   RO          873    i.LL_ADC_GetCommonPathInternalCh  stm32g0xx_hal_adc.o
    0x08004538   0x08004538   0x00000010   Code   RO          875    i.LL_ADC_IsEnabled  stm32g0xx_hal_adc.o
    0x08004548   0x08004548   0x0000000e   Code   RO          876    i.LL_ADC_IsInternalRegulatorEnabled  stm32g0xx_hal_adc.o
    0x08004556   0x08004556   0x0000000c   Code   RO          877    i.LL_ADC_REG_IsConversionOngoing  stm32g0xx_hal_adc.o
    0x08004562   0x08004562   0x00000016   Code   RO          878    i.LL_ADC_REG_IsTriggerSourceSWStart  stm32g0xx_hal_adc.o
    0x08004578   0x08004578   0x00000010   Code   RO          879    i.LL_ADC_REG_StartConversion  stm32g0xx_hal_adc.o
    0x08004588   0x08004588   0x0000003c   Code   RO          880    i.LL_ADC_SetAnalogWDMonitChannels  stm32g0xx_hal_adc.o
    0x080045c4   0x080045c4   0x0000000e   Code   RO          881    i.LL_ADC_SetCommonPathInternalCh  stm32g0xx_hal_adc.o
    0x080045d2   0x080045d2   0x0000001e   Code   RO          882    i.LL_ADC_SetSamplingTimeCommonChannels  stm32g0xx_hal_adc.o
    0x080045f0   0x080045f0   0x00000032   Code   RO         3827    i.Lcd_Clear         bsp_lcd.o
    0x08004622   0x08004622   0x00000046   Code   RO         3828    i.Lcd_ClearLine     bsp_lcd.o
    0x08004668   0x08004668   0x00000034   Code   RO         3829    i.Lcd_Init          bsp_lcd.o
    0x0800469c   0x0800469c   0x00000056   Code   RO         3830    i.Lcd_SetRegion     bsp_lcd.o
    0x080046f2   0x080046f2   0x00000002   PAD
    0x080046f4   0x080046f4   0x0000002c   Code   RO         3833    i.Lcd_WriteData     bsp_lcd.o
    0x08004720   0x08004720   0x0000002c   Code   RO         3834    i.Lcd_WriteIndex    bsp_lcd.o
    0x0800474c   0x0800474c   0x00000104   Code   RO          269    i.MX_ADC1_Init      adc.o
    0x08004850   0x08004850   0x00000044   Code   RO          317    i.MX_DMA_Init       dma.o
    0x08004894   0x08004894   0x00000108   Code   RO          225    i.MX_GPIO_Init      gpio.o
    0x0800499c   0x0800499c   0x00000074   Code   RO          395    i.MX_USART1_UART_Init  usart.o
    0x08004a10   0x08004a10   0x00000044   Code   RO          396    i.MX_USART2_UART_Init  usart.o
    0x08004a54   0x08004a54   0x00000002   Code   RO          441    i.NMI_Handler       stm32g0xx_it.o
    0x08004a56   0x08004a56   0x00000002   Code   RO          442    i.PendSV_Handler    stm32g0xx_it.o
    0x08004a58   0x08004a58   0x00000058   Code   RO         3940    i.RGB_Send_Data     bsp_p9813.o
    0x08004ab0   0x08004ab0   0x00000324   Code   RO         3769    i.Rec_WaitAT        bsp_esp8266.o
    0x08004dd4   0x08004dd4   0x000003a8   Code   RO         3770    i.SEND_TASK         bsp_esp8266.o
    0x0800517c   0x0800517c   0x00000034   Code   RO         3836    i.SPI_WriteData     bsp_lcd.o
    0x080051b0   0x080051b0   0x00000002   Code   RO          443    i.SVC_Handler       stm32g0xx_it.o
    0x080051b2   0x080051b2   0x00000008   Code   RO          444    i.SysTick_Handler   stm32g0xx_it.o
    0x080051ba   0x080051ba   0x00000002   PAD
    0x080051bc   0x080051bc   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x08005250   0x08005250   0x00000010   Code   RO         3677    i.SystemInit        system_stm32g0xx.o
    0x08005260   0x08005260   0x00000010   Code   RO          445    i.TIM1_BRK_UP_TRG_COM_IRQHandler  stm32g0xx_it.o
    0x08005270   0x08005270   0x00000084   Code   RO         3592    i.UARTEx_SetNbDataToProcess  stm32g0xx_hal_uart_ex.o
    0x080052f4   0x080052f4   0x000000f0   Code   RO         3242    i.UART_AdvFeatureConfig  stm32g0xx_hal_uart.o
    0x080053e4   0x080053e4   0x00000074   Code   RO         3243    i.UART_CheckIdleState  stm32g0xx_hal_uart.o
    0x08005458   0x08005458   0x00000018   Code   RO         3244    i.UART_DMAAbortOnError  stm32g0xx_hal_uart.o
    0x08005470   0x08005470   0x0000005a   Code   RO         3245    i.UART_DMAError     stm32g0xx_hal_uart.o
    0x080054ca   0x080054ca   0x0000004c   Code   RO         3246    i.UART_DMAReceiveCplt  stm32g0xx_hal_uart.o
    0x08005516   0x08005516   0x0000000e   Code   RO         3248    i.UART_DMARxHalfCplt  stm32g0xx_hal_uart.o
    0x08005524   0x08005524   0x0000002c   Code   RO         3254    i.UART_EndRxTransfer  stm32g0xx_hal_uart.o
    0x08005550   0x08005550   0x00000022   Code   RO         3255    i.UART_EndTransmit_IT  stm32g0xx_hal_uart.o
    0x08005572   0x08005572   0x00000022   Code   RO         3256    i.UART_EndTxTransfer  stm32g0xx_hal_uart.o
    0x08005594   0x08005594   0x00000654   Code   RO         3261    i.UART_SetConfig    stm32g0xx_hal_uart.o
    0x08005be8   0x08005be8   0x00000070   Code   RO         3266    i.UART_WaitOnFlagUntilTimeout  stm32g0xx_hal_uart.o
    0x08005c58   0x08005c58   0x00000070   Code   RO          446    i.USART2_IRQHandler  stm32g0xx_it.o
    0x08005cc8   0x08005cc8   0x00000020   Code   RO         4342    i.__0printf$8       mc_p.l(printf8.o)
    0x08005ce8   0x08005ce8   0x00000028   Code   RO         4344    i.__0sprintf$8      mc_p.l(printf8.o)
    0x08005d10   0x08005d10   0x0000002e   Code   RO         4434    i.__ARM_clz         mf_p.l(depilogue.o)
    0x08005d3e   0x08005d3e   0x0000001c   Code   RO         1605    i.__ARM_common_switch8  stm32g0xx_hal_dma.o
    0x08005d5a   0x08005d5a   0x00000002   PAD
    0x08005d5c   0x08005d5c   0x00000078   Code   RO         1867    i.__NVIC_SetPriority  stm32g0xx_hal_cortex.o
    0x08005dd4   0x08005dd4   0x0000000e   Code   RO         4459    i.__scatterload_copy  mc_p.l(handlers.o)
    0x08005de2   0x08005de2   0x00000002   Code   RO         4460    i.__scatterload_null  mc_p.l(handlers.o)
    0x08005de4   0x08005de4   0x0000000e   Code   RO         4461    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x08005df2   0x08005df2   0x00000002   PAD
    0x08005df4   0x08005df4   0x00000428   Code   RO         4349    i._printf_core      mc_p.l(printf8.o)
    0x0800621c   0x0800621c   0x00000020   Code   RO         4350    i._printf_post_padding  mc_p.l(printf8.o)
    0x0800623c   0x0800623c   0x0000002c   Code   RO         4351    i._printf_pre_padding  mc_p.l(printf8.o)
    0x08006268   0x08006268   0x0000000a   Code   RO         4353    i._sputc            mc_p.l(printf8.o)
    0x08006272   0x08006272   0x00000002   PAD
    0x08006274   0x08006274   0x00000180   Code   RO         3733    i.analysis_Packet   fs_protocol.o
    0x080063f4   0x080063f4   0x00000158   Code   RO          576    i.cmdAT             esp8266.o
    0x0800654c   0x0800654c   0x0000001a   Code   RO         3977    i.compareTime       bsp_softwaretimer.o
    0x08006566   0x08006566   0x0000003e   Code   RO         3707    i.crc8              fs_crc.o
    0x080065a4   0x080065a4   0x0000000c   Code   RO          699    i.delay_init        dht11.o
    0x080065b0   0x080065b0   0x00000048   Code   RO          701    i.delay_us          dht11.o
    0x080065f8   0x080065f8   0x00000018   Code   RO           15    i.fputc             main.o
    0x08006610   0x08006610   0x00000124   Code   RO         4035    i.get_SensorData    app_system.o
    0x08006734   0x08006734   0x000000b0   Code   RO         4003    i.get_ultrasonic_val  bsp_ultrasonic.o
    0x080067e4   0x080067e4   0x00000318   Code   RO          577    i.initEsp8266       esp8266.o
    0x08006afc   0x08006afc   0x000000f0   Code   RO           16    i.main              main.o
    0x08006bec   0x08006bec   0x00000070   Code   RO         3734    i.packet_data       fs_protocol.o
    0x08006c5c   0x08006c5c   0x00000038   Code   RO          578    i.reset_rxbuffClear  esp8266.o
    0x08006c94   0x08006c94   0x00000028   Code   RO         3942    i.rgb_setValue      bsp_p9813.o
    0x08006cbc   0x08006cbc   0x00000010   Code   RO         3978    i.setTime           bsp_softwaretimer.o
    0x08006ccc   0x08006ccc   0x00000024   Code   RO         4094    i.set_Beep_Status   bsp_beep.o
    0x08006cf0   0x08006cf0   0x000000ec   Code   RO          579    i.testAT            esp8266.o
    0x08006ddc   0x08006ddc   0x00000078   Data   RO          581    .constdata          esp8266.o
    0x08006e54   0x08006e54   0x00000060   Data   RO         3678    .constdata          system_stm32g0xx.o
    0x08006eb4   0x08006eb4   0x00001834   Data   RO         3839    .constdata          bsp_lcd.o
    0x080086e8   0x080086e8   0x00000040   Data   RO         4455    .constdata          mc_p.l(ctype_c.o)
    0x08008728   0x08008728   0x000000d3   Data   RO         3772    .conststring        bsp_esp8266.o
    0x080087fb   0x080087fb   0x00000001   PAD
    0x080087fc   0x080087fc   0x00000020   Data   RO         4457    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800881c, Size: 0x000012e0, Max: 0x00002000, ABSOLUTE, COMPRESSED[0x00000058])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000009   Data   RW          702    .data               dht11.o
    0x20000009   COMPRESSED   0x00000003   PAD
    0x2000000c   COMPRESSED   0x0000000c   Data   RW         1993    .data               stm32g0xx_hal.o
    0x20000018   COMPRESSED   0x00000004   Data   RW         3679    .data               system_stm32g0xx.o
    0x2000001c   COMPRESSED   0x00000004   PAD
    0x20000020   COMPRESSED   0x00000144   Data   RW         3773    .data               bsp_esp8266.o
    0x20000164   COMPRESSED   0x00000010   Data   RW         4037    .data               app_system.o
    0x20000174   COMPRESSED   0x00000004   Data   RW         4128    .data               mc_p.l(strtok.o)
    0x20000178   COMPRESSED   0x00000004   Data   RW         4422    .data               mc_p.l(stdout.o)
    0x2000017c        -       0x000000d0   Zero   RW          270    .bss                adc.o
    0x2000024c        -       0x000000c0   Zero   RW          343    .bss                tim.o
    0x2000030c        -       0x00000780   Zero   RW          397    .bss                usart.o
    0x20000a8c        -       0x0000000e   Zero   RW          580    .bss                esp8266.o
    0x20000a9a        -       0x00000032   Zero   RW         3771    .bss                bsp_esp8266.o
    0x20000acc        -       0x00000010   Zero   RW         4036    .bss                app_system.o
    0x20000adc   COMPRESSED   0x00000004   PAD
    0x20000ae0        -       0x00000800   Zero   RW            1    STACK               startup_stm32g030xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       448         38          0          0        208       1897   adc.o
      1536        258          0         16         16       4111   app_system.o
        36          4          0          0          0        466   bsp_beep.o
      2676        914        211        324         50       5499   bsp_esp8266.o
       658         24       6196          0          0       6741   bsp_lcd.o
       180          6          0          0          0       1680   bsp_p9813.o
        42          0          0          0          0       1308   bsp_softwaretimer.o
       176         12          0          0          0        521   bsp_ultrasonic.o
       630         66          0          9          0       5302   dht11.o
        68          4          0          0          0        662   dma.o
      2356       1084        120          0         14       8790   esp8266.o
        62          0          0          0          0       1187   fs_crc.o
       496         68          0          0          0       2056   fs_protocol.o
       264          8          0          0          0        883   gpio.o
        32         10          0          0          0        382   key.o
       456         58          0          0          0     398037   main.o
        28          8        192          0       2048        596   startup_stm32g030xx.o
       220         38          0         12          0       3577   stm32g0xx_hal.o
      3486        126          0          0          0      47000   stm32g0xx_hal_adc.o
       222         26          0          0          0      11973   stm32g0xx_hal_cortex.o
      1216         42          0          0          0       6285   stm32g0xx_hal_dma.o
       494         16          0          0          0       2679   stm32g0xx_hal_gpio.o
        68          4          0          0          0        738   stm32g0xx_hal_msp.o
        88         12          0          0          0        714   stm32g0xx_hal_pwr_ex.o
      2208         96          0          0          0      20293   stm32g0xx_hal_rcc.o
       488         14          0          0          0       1524   stm32g0xx_hal_rcc_ex.o
       512          6          0          0          0       3330   stm32g0xx_hal_tim.o
         6          0          0          0          0       1964   stm32g0xx_hal_tim_ex.o
      3768         70          0          0          0      14446   stm32g0xx_hal_uart.o
       412         18          0          0          0       4966   stm32g0xx_hal_uart_ex.o
       322         48          0          0          0       4767   stm32g0xx_it.o
        16          6         96          4          0       1019   system_stm32g0xx.o
         0          0          0          0        192        241   tim.o
       504         38          0          0       1920       2772   usart.o

    ----------------------------------------------------------------------
     24200       <USER>       <GROUP>        372       4452     568406   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          1          7          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60         10          0          0          0         84   __0sscanf.o
        86          0          0          0          0          0   __dczerorl2.o
       824         12          0          0          0         84   _scanf.o
       232          0          0          0          0         84   _scanf_str.o
        68          0          0          0          0         76   _sgetc.o
        36          4         64          0          0         60   ctype_c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        40          0          0          0          0         72   idiv.o
        36          8          0          0          0         68   init.o
        14          0          0          0          0         68   isspace_c.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
      1222         56          0          0          0        468   printf8.o
        44          8          0          0          0         84   scanf_char.o
         0          0          0          4          0          0   stdout.o
        14          0          0          0          0         60   strlen.o
        40          0          0          0          0         72   strstr.o
        72          4          0          4          0         72   strtok.o
        44          0          0          0          0         72   uidiv.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdcmple.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        60         10          0          0          0         68   dfixui.o
        28          4          0          0          0         68   dfltui.o

    ----------------------------------------------------------------------
      3732        <USER>         <GROUP>          8          0       2308   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3116        110         64          8          0       1804   mc_p.l
       604         22          0          0          0        504   mf_p.l

    ----------------------------------------------------------------------
      3732        <USER>         <GROUP>          8          0       2308   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     27932       3254       6912        380       4452     562878   Grand Totals
     27932       3254       6912         88       4452     562878   ELF Image Totals (compressed)
     27932       3254       6912         88          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                34844 (  34.03kB)
    Total RW  Size (RW Data + ZI Data)              4832 (   4.72kB)
    Total ROM Size (Code + RO Data + RW Data)      34932 (  34.11kB)

==============================================================================

